#!/usr/bin/env python3
import os
import time
import pyautogui
import requests
import base64
import json
import pyttsx3
import mss
from typing import Tuple, Optional
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class CAPTCHASolver:
    def __init__(self):
        # Initialize text-to-speech engine
        self.engine = pyttsx3.init()
        self.engine.setProperty('rate', 150)
        self.engine.setProperty('volume', 1.0)
        
        # API configuration
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.gemini_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
        
        # Screen capture configuration
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Safety configurations
        pyautogui.PAUSE = 1.0
        pyautogui.FAILSAFE = True
        
        self.speak("CAPTCHA Solver initialized and ready to assist.")
    
    def speak(self, text: str) -> None:
        """Convert text to speech for auditory feedback"""
        print(f"[SPEECH] {text}")
        self.engine.say(text)
        self.engine.runAndWait()
    
    def capture_screen_region(self, region: Tuple[int, int, int, int] = None) -> str:
        """
        Capture a screenshot of the specified region and return as base64
        Args:
            region: tuple of (left, top, width, height)
        Returns:
            Base64 encoded image string
        """
        with mss.mss() as sct:
            if region:
                monitor = {
                    "top": region[1],
                    "left": region[0],
                    "width": region[2],
                    "height": region[3],
                    "mon": 1
                }
            else:
                monitor = sct.monitors[1]  # Primary monitor
            
            screenshot = sct.grab(monitor)
            img_bytes = mss.tools.to_png(screenshot.rgb, screenshot.size)
            return base64.b64encode(img_bytes).decode('utf-8')
    
    def analyze_with_gemini(self, image_b64: str, prompt: str) -> dict:
        """Analyze the CAPTCHA using Gemini API"""
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "contents": [{
                "parts": [
                    {"text": prompt},
                    {
                        "inline_data": {
                            "mime_type": "image/png",
                            "data": image_b64
                        }
                    }
                ]
            }]
        }
        
        try:
            response = requests.post(
                f"{self.gemini_url}?key={self.gemini_api_key}",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.speak(f"Error analyzing CAPTCHA: {str(e)}")
            raise
    
    def parse_gemini_response(self, response: dict) -> Tuple[Tuple[int, int], str]:
        """
        Parse Gemini response to get coordinates and instructions
        
        Expected response format:
        "Click at (x,y) to solve the CAPTCHA"
        "Select images containing [object]"
        """
        try:
            text = response['candidates'][0]['content']['parts'][0]['text']
            self.speak(f"Analysis complete. Guidance: {text}")
            
            # Extract coordinates if they exist
            if '(' in text and ')' in text:
                coord_part = text.split('(')[1].split(')')[0]
                x, y = map(int, coord_part.split(','))
                return (x, y), text
            
            return None, text
        except Exception as e:
            self.speak("Could not understand the CAPTCHA solution instructions.")
            raise
    
    def solve_reCAPTCHA(self) -> bool:
        """Main workflow for solving reCAPTCHA"""
        self.speak("Starting reCAPTCHA solving process.")
        
        try:
            # Step 1: Identify the reCAPTCHA checkbox
            prompt = """
            Locate the reCAPTCHA checkbox on this webpage. If present, provide the exact center coordinates 
            in format (x,y) where I should click to select it. The checkbox is usually a square with 
            "I'm not a robot" text near it.
            """
            
            # Capture the visible screen area (adjust region as needed)
            screenshot = self.capture_screen_region()
            response = self.analyze_with_gemini(screenshot, prompt)
            checkbox_coords, _ = self.parse_gemini_response(response)
            
            if checkbox_coords:
                x, y = checkbox_coords
                self.speak(f"Clicking reCAPTCHA checkbox at coordinates {x}, {y}")
                pyautogui.click(x, y)
                time.sleep(2)  # Wait for CAPTCHA to load
            else:
                self.speak("Could not locate reCAPTCHA checkbox automatically.")
                return False
            
            # Step 2: Handle image recognition challenge if it appears
            screenshot = self.capture_screen_region()
            prompt = """
            Analyze this CAPTCHA challenge. If it's an image selection challenge (e.g., "select all images 
            with traffic lights"), provide exact center coordinates (x,y) for each image that matches the 
            description. Format your response as "Click at (x1,y1), (x2,y2)... to solve the CAPTCHA". 
            If it's audio CAPTCHA, indicate that.
            """
            
            response = self.analyze_with_gemini(screenshot, prompt)
            coords, instructions = self.parse_gemini_response(response)
            
            if "audio" in instructions.lower():
                self.speak("Audio CAPTCHA detected. Switching to audio challenge.")
                return self.solve_audio_captcha()
            
            if coords:
                if isinstance(coords[0], tuple):  # Multiple coordinates
                    for (x, y) in coords:
                        self.speak(f"Clicking at {x}, {y}")
                        pyautogui.click(x, y)
                        time.sleep(0.5)
                else:  # Single coordinate pair
                    x, y = coords
                    self.speak(f"Clicking at {x}, {y}")
                    pyautogui.click(x, y)
                
                # Click verify/submit button if needed
                time.sleep(1)
                screenshot = self.capture_screen_region()
                prompt = """
                Locate the verify or submit button for this CAPTCHA challenge. 
                If present, provide the exact center coordinates (x,y) to click it.
                """
                
                response = self.analyze_with_gemini(screenshot, prompt)
                button_coords, _ = self.parse_gemini_response(response)
                
                if button_coords:
                    x, y = button_coords
                    self.speak(f"Clicking verify button at {x}, {y}")
                    pyautogui.click(x, y)
                    time.sleep(1)
                
                self.speak("reCAPTCHA challenge completed.")
                return True
            
            return False
        except Exception as e:
            self.speak(f"Error solving reCAPTCHA: {str(e)}")
            return False
    
    def solve_cloudflare(self) -> bool:
        """Main workflow for solving Cloudflare CAPTCHA"""
        self.speak("Starting Cloudflare challenge solver.")
        
        try:
            # Step 1: Identify the Cloudflare challenge
            prompt = """
            Locate the Cloudflare challenge on this webpage. Provide the exact center coordinates 
            (x,y) of the main verification button which typically says "Verify you are human". 
            Also indicate if this is a turnstile or other type of Cloudflare challenge.
            """
            
            screenshot = self.capture_screen_region()
            response = self.analyze_with_gemini(screenshot, prompt)
            button_coords, instructions = self.parse_gemini_response(response)
            
            # Handle Turnstile (usually just needs a click)
            if "turnstile" in instructions.lower() and button_coords:
                x, y = button_coords
                self.speak(f"Clicking Cloudflare verification at {x}, {y}")
                pyautogui.click(x, y)
                time.sleep(2)
                return True
            
            # Handle other Cloudflare challenges
            prompt = """
            This appears to be a Cloudflare CAPTCHA challenge. Provide detailed instructions 
            and exact coordinates of elements to interact with to solve this challenge. 
            Typical elements may include checkboxes, verify buttons, or image selections.
            Format coordinates as (x,y).
            """
            
            screenshot = self.capture_screen_region()
            response = self.analyze_with_gemini(screenshot, prompt)
            coords, instructions = self.parse_gemini_response(response)
            
            if coords:
                if isinstance(coords[0], tuple):
                    for (x, y) in coords:
                        self.speak(f"Clicking at {x}, {y}")
                        pyautogui.click(x, y)
                        time.sleep(0.5)
                else:
                    x, y = coords
                    self.speak(f"Clicking at {x}, {y}")
                    pyautogui.click(x, y)
                
                time.sleep(2)
                self.speak("Cloudflare challenge completed.")
                return True
            
            return False
        except Exception as e:
            self.speak(f"Error solving Cloudflare challenge: {str(e)}")
            return False
    
    def solve_audio_captcha(self) -> bool:
        """Handle audio CAPTCHA challenges"""
        self.speak("Starting audio CAPTCHA solver.")
        
        try:
            # Tell user to click the audio button if not auto-detected
            prompt = """
            Locate the audio CAPTCHA play button on this webpage. Provide the exact center coordinates 
            (x,y) to click to play the audio CAPTCHA. Also identify any text entry field for the solution.
            """
            
            screenshot = self.capture_screen_region()
            response = self.analyze_with_gemini(screenshot, prompt)
            elements, instructions = self.parse_gemini_response(response)
            
            if not elements:
                self.speak("Please manually click the audio CAPTCHA button then type 'done'.")
                while True:
                    user_input = input("Type 'done' after clicking play or 'exit' to cancel: ").strip().lower()
                    if user_input == 'done':
                        break
                    if user_input == 'exit':
                        return False
            
            self.speak("Please listen to the audio CAPTCHA and speak the solution.")
            solution = input("Enter the audio CAPTCHA solution: ").strip()
            
            prompt = f"""
            Identify the text input field where the audio CAPTCHA solution should be entered.
            Provide the exact center coordinates (x,y). The solution is: {solution}
            """
            
            screenshot = self.capture_screen_region()
            response = self.analyze_with_gemini(screenshot, prompt)
            input_coords, _ = self.parse_gemini_response(response)
            
            if input_coords:
                x, y = input_coords
                self.speak(f"Typing solution at {x}, {y}")
                pyautogui.click(x, y)
                pyautogui.write(solution, interval=0.1)
                time.sleep(0.5)
                
                # Click submit if needed
                prompt = """
                Locate the submit or verify button for this audio CAPTCHA solution.
                Provide the exact center coordinates (x,y) to click it.
                """
                
                response = self.analyze_with_gemini(screenshot, prompt)
                button_coords, _ = self.parse_gemini_response(response)
                
                if button_coords:
                    x, y = button_coords
                    self.speak(f"Submitting solution at {x}, {y}")
                    pyautogui.click(x, y)
                    time.sleep(1)
                
                self.speak("Audio CAPTCHA challenge completed.")
                return True
            
            return False
        except Exception as e:
            self.speak(f"Error solving audio CAPTCHA: {str(e)}")
            return False
    
    def run(self):
        """Main execution method with user interaction"""
        self.speak("Welcome to AccessiCAPTCHA. I'll help you solve CAPTCHA challenges.")
        
        try:
            while True:
                self.speak("Please say or type what type of challenge you need help with: reCAPTCHA, Cloudflare, or exit.")
                choice = input("Enter challenge type (reCAPTCHA/cloudflare/exit): ").strip().lower()
                
                if choice == 'exit':
                    self.speak("Goodbye! Exiting AccessiCAPTCHA.")
                    break
                elif choice in ['recaptcha', 'reCAPTCHA', '1']:
                    success = self.solve_reCAPTCHA()
                    if not success:
                        self.speak("Could not automatically solve the reCAPTCHA. Please try again with a clear view of the challenge.")
                elif choice in ['cloudflare', 'cf', '2']:
                    success = self.solve_cloudflare()
                    if not success:
                        self.speak("Could not automatically solve the Cloudflare challenge. Please try again with a clear view.")
                else:
                    self.speak("I didn't understand that. Please say reCAPTCHA, Cloudflare, or exit.")
        except KeyboardInterrupt:
            self.speak("Program interrupted by user. Goodbye!")
        except Exception as e:
            self.speak(f"An unexpected error occurred: {str(e)}")

if __name__ == "__main__":
    # Check requirements
    try:
        solver = CAPTCHASolver()
        solver.run()
    except Exception as e:
        import traceback
        print(f"Failed to initialize CAPTCHA solver: {str(e)}")
        print(traceback.format_exc())
        input("Press Enter to exit...")
