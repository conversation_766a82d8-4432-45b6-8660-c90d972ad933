import tkinter as tk

def draw_box_at(x, y, width=2, height=2, border_color='red', border_width=4):
    root = tk.Tk()
    root.overrideredirect(True)   # Remove window decorations
    root.attributes('-topmost', True)  # Always on top
    root.geometry(f"{width}x{height}+{x}+{y}")

    # Transparent background except the box border
    root.wm_attributes('-transparentcolor', 'grey')
    canvas = tk.Canvas(root, width=width, height=height, bg='grey', highlightthickness=0)
    canvas.pack()

    # Draw the rectangle border
    canvas.create_rectangle(
        border_width // 2, border_width // 2, width - border_width // 2, height - border_width // 2,
        outline=border_color, width=border_width
    )

    root.mainloop()

if __name__ == "__main__":
    # Example: Draw a box at (300, 250) with default size 200x150
    draw_box_at(71, 385)
