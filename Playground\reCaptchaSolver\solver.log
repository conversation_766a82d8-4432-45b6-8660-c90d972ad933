2025-07-09 16:01:30,566 - INFO - Could not import comtypes.gen, trying to create it.
2025-07-09 16:01:30,568 - INFO - Created comtypes.gen directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:01:30,568 - INFO - Writing __init__.py file: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen\__init__.py'
2025-07-09 16:01:30,613 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:01:30,651 - INFO - Could not import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4: No module named 'comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4'
2025-07-09 16:01:30,680 - INFO - # Generating comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4
2025-07-09 16:01:30,719 - INFO - # Generating comtypes.gen.SpeechLib
2025-07-09 16:01:30,725 - INFO - Could not import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0: No module named 'comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0'
2025-07-09 16:01:30,726 - INFO - # Generating comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0
2025-07-09 16:01:30,729 - INFO - # Generating comtypes.gen.stdole
2025-07-09 16:01:31,351 - INFO - CaptchaSolver initialized
2025-07-09 16:01:31,351 - INFO - Speaking: Captcha solver ready
2025-07-09 16:01:33,785 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:01:36,508 - INFO - Attempt 1/3
2025-07-09 16:01:36,508 - INFO - Speaking: Attempt 1
2025-07-09 16:01:38,512 - INFO - Screenshot saved: screenshots\challenge_1752057098.png
2025-07-09 16:01:41,807 - INFO - Speaking: No challenge detected
2025-07-09 16:03:28,629 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:03:28,629 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:03:28,923 - INFO - CaptchaSolver initialized
2025-07-09 16:03:28,923 - INFO - Speaking: Captcha solver ready
2025-07-09 16:03:31,299 - INFO - Speaking: Detecting Cloudflare challenge
2025-07-09 16:03:34,030 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:03:36,669 - INFO - Attempt 1/3
2025-07-09 16:03:36,669 - INFO - Speaking: Attempt 1
2025-07-09 16:03:38,651 - INFO - Screenshot saved: screenshots\challenge_1752057218.png
2025-07-09 16:03:42,526 - INFO - Executing solution for cloudflare
2025-07-09 16:03:42,526 - INFO - Speaking: Solving cloudflare challenge
2025-07-09 16:03:45,213 - INFO - Speaking: Click the 'Verify you are human' checkbox. Then click the 'Check' button to proceed. After the verification, check if the page redirects or displays any further instructions.
2025-07-09 16:03:57,587 - INFO - Speaking: Clicking Verify you are human checkbox
2025-07-09 16:04:00,891 - INFO - Clicking at (422, 402) - Verify you are human checkbox
2025-07-09 16:04:04,180 - INFO - Speaking: Clicking Check button
2025-07-09 16:04:06,263 - INFO - Clicking at (376, 476) - Check button
2025-07-09 16:04:09,523 - INFO - Speaking: Waiting for verification
2025-07-09 16:04:18,564 - INFO - Challenge still present, may need additional steps
2025-07-09 16:04:20,566 - INFO - Attempt 2/3
2025-07-09 16:04:20,566 - INFO - Speaking: Attempt 2
2025-07-09 16:04:22,303 - INFO - Screenshot saved: screenshots\challenge_1752057262.png
2025-07-09 16:04:25,382 - INFO - Executing solution for cloudflare
2025-07-09 16:04:25,382 - INFO - Speaking: Solving cloudflare challenge
2025-07-09 16:04:28,066 - INFO - Speaking: Click the "Verify you are human" checkbox to initiate the Cloudflare Turnstile challenge.
2025-07-09 16:04:33,917 - INFO - Speaking: Clicking Verify you are human
2025-07-09 16:04:36,555 - INFO - Clicking at (451, 374) - Verify you are human
2025-07-09 16:04:39,835 - INFO - Speaking: Waiting for verification
2025-07-09 16:04:48,028 - INFO - Speaking: Challenge solved successfully
2025-07-09 16:09:26,024 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:09:26,025 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:09:26,342 - INFO - CaptchaSolver initialized
2025-07-09 16:09:26,342 - INFO - Speaking: Captcha solver ready
2025-07-09 16:09:28,733 - INFO - Speaking: Detecting reCAPTCHA challenge
2025-07-09 16:09:32,411 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:09:35,058 - INFO - Attempt 1/3
2025-07-09 16:09:35,058 - INFO - Speaking: Attempt 1
2025-07-09 16:09:37,017 - INFO - Screenshot saved: screenshots\challenge_1752057576.png
2025-07-09 16:09:40,770 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:09:40,770 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:09:44,423 - INFO - Speaking: Click the checkbox labeled 'I'm not a robot' to initiate the reCAPTCHA challenge.
2025-07-09 16:09:50,744 - INFO - Speaking: Clicking I'm not a robot checkbox
2025-07-09 16:09:53,720 - INFO - Clicking at (390, 369) - I'm not a robot checkbox
2025-07-09 16:09:56,992 - INFO - Speaking: Waiting for verification
2025-07-09 16:10:05,181 - INFO - Speaking: Challenge solved successfully
