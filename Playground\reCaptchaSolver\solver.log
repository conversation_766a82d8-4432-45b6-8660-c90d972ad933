2025-07-09 16:01:30,566 - INFO - Could not import comtypes.gen, trying to create it.
2025-07-09 16:01:30,568 - INFO - Created comtypes.gen directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:01:30,568 - INFO - Writing __init__.py file: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen\__init__.py'
2025-07-09 16:01:30,613 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:01:30,651 - INFO - Could not import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4: No module named 'comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4'
2025-07-09 16:01:30,680 - INFO - # Generating comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4
2025-07-09 16:01:30,719 - INFO - # Generating comtypes.gen.SpeechLib
2025-07-09 16:01:30,725 - INFO - Could not import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0: No module named 'comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0'
2025-07-09 16:01:30,726 - INFO - # Generating comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0
2025-07-09 16:01:30,729 - INFO - # Generating comtypes.gen.stdole
2025-07-09 16:01:31,351 - INFO - CaptchaSolver initialized
2025-07-09 16:01:31,351 - INFO - Speaking: Captcha solver ready
2025-07-09 16:01:33,785 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:01:36,508 - INFO - Attempt 1/3
2025-07-09 16:01:36,508 - INFO - Speaking: Attempt 1
2025-07-09 16:01:38,512 - INFO - Screenshot saved: screenshots\challenge_1752057098.png
2025-07-09 16:01:41,807 - INFO - Speaking: No challenge detected
2025-07-09 16:03:28,629 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:03:28,629 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:03:28,923 - INFO - CaptchaSolver initialized
2025-07-09 16:03:28,923 - INFO - Speaking: Captcha solver ready
2025-07-09 16:03:31,299 - INFO - Speaking: Detecting Cloudflare challenge
2025-07-09 16:03:34,030 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:03:36,669 - INFO - Attempt 1/3
2025-07-09 16:03:36,669 - INFO - Speaking: Attempt 1
2025-07-09 16:03:38,651 - INFO - Screenshot saved: screenshots\challenge_1752057218.png
2025-07-09 16:03:42,526 - INFO - Executing solution for cloudflare
2025-07-09 16:03:42,526 - INFO - Speaking: Solving cloudflare challenge
2025-07-09 16:03:45,213 - INFO - Speaking: Click the 'Verify you are human' checkbox. Then click the 'Check' button to proceed. After the verification, check if the page redirects or displays any further instructions.
2025-07-09 16:03:57,587 - INFO - Speaking: Clicking Verify you are human checkbox
2025-07-09 16:04:00,891 - INFO - Clicking at (422, 402) - Verify you are human checkbox
2025-07-09 16:04:04,180 - INFO - Speaking: Clicking Check button
2025-07-09 16:04:06,263 - INFO - Clicking at (376, 476) - Check button
2025-07-09 16:04:09,523 - INFO - Speaking: Waiting for verification
2025-07-09 16:04:18,564 - INFO - Challenge still present, may need additional steps
2025-07-09 16:04:20,566 - INFO - Attempt 2/3
2025-07-09 16:04:20,566 - INFO - Speaking: Attempt 2
2025-07-09 16:04:22,303 - INFO - Screenshot saved: screenshots\challenge_1752057262.png
2025-07-09 16:04:25,382 - INFO - Executing solution for cloudflare
2025-07-09 16:04:25,382 - INFO - Speaking: Solving cloudflare challenge
2025-07-09 16:04:28,066 - INFO - Speaking: Click the "Verify you are human" checkbox to initiate the Cloudflare Turnstile challenge.
2025-07-09 16:04:33,917 - INFO - Speaking: Clicking Verify you are human
2025-07-09 16:04:36,555 - INFO - Clicking at (451, 374) - Verify you are human
2025-07-09 16:04:39,835 - INFO - Speaking: Waiting for verification
2025-07-09 16:04:48,028 - INFO - Speaking: Challenge solved successfully
2025-07-09 16:09:26,024 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:09:26,025 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:09:26,342 - INFO - CaptchaSolver initialized
2025-07-09 16:09:26,342 - INFO - Speaking: Captcha solver ready
2025-07-09 16:09:28,733 - INFO - Speaking: Detecting reCAPTCHA challenge
2025-07-09 16:09:32,411 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:09:35,058 - INFO - Attempt 1/3
2025-07-09 16:09:35,058 - INFO - Speaking: Attempt 1
2025-07-09 16:09:37,017 - INFO - Screenshot saved: screenshots\challenge_1752057576.png
2025-07-09 16:09:40,770 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:09:40,770 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:09:44,423 - INFO - Speaking: Click the checkbox labeled 'I'm not a robot' to initiate the reCAPTCHA challenge.
2025-07-09 16:09:50,744 - INFO - Speaking: Clicking I'm not a robot checkbox
2025-07-09 16:09:53,720 - INFO - Clicking at (390, 369) - I'm not a robot checkbox
2025-07-09 16:09:56,992 - INFO - Speaking: Waiting for verification
2025-07-09 16:10:05,181 - INFO - Speaking: Challenge solved successfully
2025-07-09 16:21:46,208 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:21:46,208 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:21:46,497 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:21:46,497 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:21:46,497 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:21:46,497 - INFO - CaptchaSolver initialized
2025-07-09 16:21:46,497 - INFO - Active monitor: 2
2025-07-09 16:21:46,498 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:21:46,498 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 16:22:41,122 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:22:41,122 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:22:41,122 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:22:49,297 - INFO - Mouse cursor detected on monitor 1
2025-07-09 16:22:49,298 - INFO - Detected 2 monitors. Active monitor: 1
2025-07-09 16:22:49,298 - INFO - Monitor offset: {'x': 1920, 'y': -9, 'width': 1920, 'height': 1080}
2025-07-09 16:22:59,758 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:22:59,758 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:22:59,758 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:26:27,632 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:26:27,632 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:26:27,923 - INFO - Speaking: Test message
2025-07-09 16:26:27,979 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:26:27,980 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:26:27,980 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:26:28,154 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:26:28,154 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:26:28,154 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:26:28,154 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:26:28,155 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:26:28,155 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:26:28,156 - INFO - Screenshot saved: screenshots\test.png
2025-07-09 16:26:28,158 - INFO - Clicking at screenshot coords (100, 200) -> actual coords (100, 200) - Test
2025-07-09 16:26:28,862 - INFO - CaptchaSolver initialized
2025-07-09 16:26:28,862 - INFO - Active monitor: <MagicMock name='ScreenCapture().active_monitor' id='2442478890896'>
2025-07-09 16:26:28,862 - INFO - Monitor offset: <MagicMock name='ScreenCapture().monitor_offset' id='2442479333264'>
2025-07-09 16:26:28,863 - INFO - CaptchaSolver initialized
2025-07-09 16:26:28,863 - INFO - Active monitor: <MagicMock name='ScreenCapture().active_monitor' id='2442479446928'>
2025-07-09 16:26:28,863 - INFO - Monitor offset: <MagicMock name='ScreenCapture().monitor_offset' id='2442479489680'>
2025-07-09 16:26:28,865 - INFO - Attempt 1/3
2025-07-09 16:26:44,367 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:26:44,367 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:26:44,675 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:26:44,675 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:26:44,675 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:26:44,675 - INFO - CaptchaSolver initialized
2025-07-09 16:26:44,675 - INFO - Active monitor: 2
2025-07-09 16:26:44,675 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:26:44,676 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 16:26:48,350 - INFO - Monitor 1: 1920x1080 at (1920, -9)
2025-07-09 16:26:48,350 - INFO - Monitor 2: 1920x1080 at (0, 0)
2025-07-09 16:26:48,350 - INFO - Speaking: Found 2 monitors
2025-07-09 16:26:55,362 - INFO - Speaking: Detecting reCAPTCHA challenge
2025-07-09 16:26:59,033 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:27:01,680 - INFO - Attempt 1/3
2025-07-09 16:27:01,680 - INFO - Speaking: Attempt 1
2025-07-09 16:27:03,661 - INFO - Screenshot saved: screenshots\challenge_1752058623.png
2025-07-09 16:27:07,438 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:27:07,438 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:27:11,086 - INFO - Speaking: The application indicates it is attempting to solve a reCAPTCHA v2 challenge.  The user needs to interact with the reCAPTCHA image selection if one appears on the screen.
2025-07-09 16:27:23,838 - INFO - Speaking: Waiting for verification
2025-07-09 16:27:33,346 - INFO - Challenge still present, may need additional steps
2025-07-09 16:27:35,347 - INFO - Attempt 2/3
2025-07-09 16:27:35,347 - INFO - Speaking: Attempt 2
2025-07-09 16:27:37,093 - INFO - Screenshot saved: screenshots\challenge_1752058657.png
2025-07-09 16:27:40,865 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:27:40,866 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:27:44,513 - INFO - Speaking: The application has detected a reCAPTCHA v2 challenge. The user needs to manually interact with the reCAPTCHA image selection if one appears on the screen.
2025-07-09 16:27:56,722 - INFO - Speaking: Waiting for verification
2025-07-09 16:28:05,852 - INFO - Challenge still present, may need additional steps
2025-07-09 16:28:07,853 - INFO - Attempt 3/3
2025-07-09 16:28:07,853 - INFO - Speaking: Attempt 3
2025-07-09 16:28:09,680 - INFO - Screenshot saved: screenshots\challenge_1752058689.png
2025-07-09 16:28:12,887 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:28:12,887 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:28:16,533 - INFO - Speaking: The application has detected a reCAPTCHA v2 challenge. The user needs to manually interact with the reCAPTCHA image selection if one appears on the screen.
2025-07-09 16:28:28,742 - INFO - Speaking: Waiting for verification
2025-07-09 16:28:59,431 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:28:59,432 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:28:59,741 - INFO - Mouse cursor detected on monitor 1
2025-07-09 16:28:59,741 - INFO - Detected 2 monitors. Active monitor: 1
2025-07-09 16:28:59,742 - INFO - Monitor offset: {'x': 1920, 'y': -9, 'width': 1920, 'height': 1080}
2025-07-09 16:28:59,742 - INFO - CaptchaSolver initialized
2025-07-09 16:28:59,742 - INFO - Active monitor: 1
2025-07-09 16:28:59,742 - INFO - Monitor offset: {'x': 1920, 'y': -9, 'width': 1920, 'height': 1080}
2025-07-09 16:28:59,742 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 16:29:03,412 - INFO - Monitor 1: 1920x1080 at (1920, -9)
2025-07-09 16:29:03,412 - INFO - Monitor 2: 1920x1080 at (0, 0)
2025-07-09 16:29:03,412 - INFO - Speaking: Found 2 monitors
2025-07-09 16:29:46,272 - INFO - Imported existing <module 'comtypes.gen' from 'X:\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:29:46,273 - INFO - Using writeable comtypes cache directory: 'X:\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:29:46,582 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:29:46,582 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:29:46,582 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:29:46,582 - INFO - CaptchaSolver initialized
2025-07-09 16:29:46,583 - INFO - Active monitor: 2
2025-07-09 16:29:46,583 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:29:46,583 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 16:29:50,259 - INFO - Monitor 1: 1920x1080 at (1920, -9)
2025-07-09 16:29:50,259 - INFO - Monitor 2: 1920x1080 at (0, 0)
2025-07-09 16:29:50,259 - INFO - Speaking: Found 2 monitors
2025-07-09 16:30:00,659 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:30:03,314 - INFO - Attempt 1/3
2025-07-09 16:30:03,315 - INFO - Speaking: Attempt 1
2025-07-09 16:30:05,307 - INFO - Screenshot saved: screenshots\challenge_1752058805.png
2025-07-09 16:30:09,234 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:30:09,235 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:30:12,882 - INFO - Speaking: Click the "I'm not a robot" checkbox. Then, if prompted, solve the image selection challenge.
2025-07-09 16:30:20,728 - INFO - Speaking: Clicking "I'm not a robot" checkbox
2025-07-09 16:30:23,854 - INFO - Clicking at screenshot coords (89, 389) -> actual coords (89, 389) - "I'm not a robot" checkbox
2025-07-09 16:30:27,117 - INFO - Speaking: Waiting for verification
2025-07-09 16:30:36,353 - INFO - Challenge still present, may need additional steps
2025-07-09 16:30:38,354 - INFO - Attempt 2/3
2025-07-09 16:30:38,354 - INFO - Speaking: Attempt 2
2025-07-09 16:30:40,085 - INFO - Screenshot saved: screenshots\challenge_1752058840.png
2025-07-09 16:30:43,617 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:30:43,618 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:30:47,265 - INFO - Speaking: Click the 'I'm not a robot' checkbox. If prompted, solve the image selection challenge.
2025-07-09 16:30:54,301 - INFO - Speaking: Clicking 'I'm not a robot' checkbox
2025-07-09 16:30:57,430 - INFO - Clicking at screenshot coords (89, 389) -> actual coords (89, 389) - 'I'm not a robot' checkbox
2025-07-09 16:31:00,711 - INFO - Speaking: Waiting for verification
2025-07-09 16:31:09,813 - INFO - Challenge still present, may need additional steps
2025-07-09 16:31:11,814 - INFO - Attempt 3/3
2025-07-09 16:31:11,814 - INFO - Speaking: Attempt 3
2025-07-09 16:31:13,646 - INFO - Screenshot saved: screenshots\challenge_1752058873.png
2025-07-09 16:31:17,158 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:31:17,158 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:31:20,808 - INFO - Speaking: Click the 'I'm not a robot' checkbox. If prompted, solve the image selection challenge.
2025-07-09 16:31:27,843 - INFO - Speaking: Clicking The 'I'm not a robot' checkbox
2025-07-09 16:31:31,148 - INFO - Clicking at screenshot coords (89, 389) -> actual coords (89, 389) - The 'I'm not a robot' checkbox
2025-07-09 16:31:34,430 - INFO - Speaking: Waiting for verification
2025-07-09 16:31:44,014 - INFO - Challenge still present, may need additional steps
2025-07-09 16:31:46,015 - INFO - Speaking: Failed to solve challenge after maximum attempts
2025-07-09 16:47:58,035 - INFO - Imported existing <module 'comtypes.gen' from '\\\\192.168.1.15\\user_data\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:47:58,036 - INFO - Using writeable comtypes cache directory: '\\192.168.1.15\user_data\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:47:58,355 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:47:58,355 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:47:58,355 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:47:58,355 - INFO - CaptchaSolver initialized
2025-07-09 16:47:58,356 - INFO - Active monitor: 2
2025-07-09 16:47:58,356 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:47:58,356 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 16:48:02,029 - INFO - Monitor 1: 1920x1080 at (1920, -9)
2025-07-09 16:48:02,029 - INFO - Monitor 2: 1920x1080 at (0, 0)
2025-07-09 16:48:02,029 - INFO - Speaking: Found 2 monitors
2025-07-09 16:48:24,963 - INFO - Current mouse position: (1624, 848)
2025-07-09 16:48:24,963 - INFO - Mouse is on monitor 2
2025-07-09 16:48:24,963 - INFO - Speaking: Mouse is currently on monitor 2
2025-07-09 16:48:36,337 - INFO - Imported existing <module 'comtypes.gen' from '\\\\192.168.1.15\\user_data\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:48:36,338 - INFO - Using writeable comtypes cache directory: '\\192.168.1.15\user_data\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:48:36,634 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:48:36,635 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:48:36,635 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:48:36,635 - INFO - CaptchaSolver initialized
2025-07-09 16:48:36,635 - INFO - Active monitor: 2
2025-07-09 16:48:36,635 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:48:36,635 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 16:48:40,312 - INFO - Monitor 1: 1920x1080 at (1920, -9)
2025-07-09 16:48:40,312 - INFO - Monitor 2: 1920x1080 at (0, 0)
2025-07-09 16:48:40,313 - INFO - Speaking: Found 2 monitors
2025-07-09 16:49:04,601 - INFO - Screenshot coords (89, 120) -> Actual coords (89, 120)
2025-07-09 16:49:04,601 - INFO - Speaking: Screenshot coordinates 89, 120 translate to actual coordinates 89, 120
2025-07-09 16:49:21,471 - INFO - Imported existing <module 'comtypes.gen' from '\\\\192.168.1.15\\user_data\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 16:49:21,471 - INFO - Using writeable comtypes cache directory: '\\192.168.1.15\user_data\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 16:49:21,756 - INFO - Mouse cursor detected on monitor 2
2025-07-09 16:49:21,756 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 16:49:21,756 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:49:21,756 - INFO - CaptchaSolver initialized
2025-07-09 16:49:21,756 - INFO - Active monitor: 2
2025-07-09 16:49:21,757 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 16:49:21,757 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 16:49:25,432 - INFO - Monitor 1: 1920x1080 at (1920, -9)
2025-07-09 16:49:25,432 - INFO - Monitor 2: 1920x1080 at (0, 0)
2025-07-09 16:49:25,432 - INFO - Speaking: Found 2 monitors
2025-07-09 16:49:35,313 - INFO - Speaking: Starting challenge analysis
2025-07-09 16:49:37,953 - INFO - Attempt 1/3
2025-07-09 16:49:37,953 - INFO - Speaking: Attempt 1
2025-07-09 16:49:39,912 - INFO - Screenshot saved: screenshots\challenge_1752059979.png
2025-07-09 16:49:44,183 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:49:44,183 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:49:47,831 - INFO - Speaking: Click the "I'm not a robot" checkbox to proceed.
2025-07-09 16:49:51,552 - INFO - Speaking: Clicking "I'm not a robot" checkbox
2025-07-09 16:49:54,678 - INFO - Clicking at screenshot coords (40, 388) -> actual coords (40, 388) - "I'm not a robot" checkbox
2025-07-09 16:49:57,937 - INFO - Speaking: Waiting for verification
2025-07-09 16:50:07,579 - INFO - Challenge still present, may need additional steps
2025-07-09 16:50:09,580 - INFO - Attempt 2/3
2025-07-09 16:50:09,580 - INFO - Speaking: Attempt 2
2025-07-09 16:50:11,302 - INFO - Screenshot saved: screenshots\challenge_1752060011.png
2025-07-09 16:50:15,404 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:50:15,404 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:50:19,052 - INFO - Speaking: Click the "I'm not a robot" checkbox to proceed with the reCAPTCHA challenge.
2025-07-09 16:50:25,172 - INFO - Speaking: Clicking "I'm not a robot" checkbox
2025-07-09 16:50:28,297 - INFO - Clicking at screenshot coords (40, 388) -> actual coords (40, 388) - "I'm not a robot" checkbox
2025-07-09 16:50:31,555 - INFO - Speaking: Waiting for verification
2025-07-09 16:50:41,201 - INFO - Challenge still present, may need additional steps
2025-07-09 16:50:43,202 - INFO - Attempt 3/3
2025-07-09 16:50:43,202 - INFO - Speaking: Attempt 3
2025-07-09 16:50:45,021 - INFO - Screenshot saved: screenshots\challenge_1752060044.png
2025-07-09 16:50:53,522 - INFO - Executing solution for recaptcha_v2
2025-07-09 16:50:53,523 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 16:50:57,171 - INFO - Speaking: Click the 'I'm not a robot' checkbox. Wait for the verification to complete. If the challenge persists, there may be additional steps required. Listen for audio prompts for further instructions.
2025-07-09 17:17:39,553 - INFO - Imported existing <module 'comtypes.gen' from '\\\\192.168.1.15\\user_data\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 17:17:39,553 - INFO - Using writeable comtypes cache directory: '\\192.168.1.15\user_data\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 17:17:39,858 - INFO - Mouse cursor detected on monitor 2
2025-07-09 17:17:39,859 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 17:17:39,859 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 17:17:39,859 - INFO - CaptchaSolver initialized
2025-07-09 17:17:39,859 - INFO - Active monitor: 2
2025-07-09 17:17:39,859 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 17:17:39,859 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 17:17:43,549 - INFO - Monitor 1: 1920x1080 at (1920, -9)
2025-07-09 17:17:43,549 - INFO - Monitor 2: 1920x1080 at (0, 0)
2025-07-09 17:17:43,549 - INFO - Speaking: Found 2 monitors
2025-07-09 17:17:48,622 - INFO - Speaking: Starting challenge analysis
2025-07-09 17:17:51,269 - INFO - Attempt 1/3
2025-07-09 17:17:51,269 - INFO - Speaking: Attempt 1
2025-07-09 17:17:53,248 - INFO - Screenshot saved: screenshots\challenge_1752061673.png
2025-07-09 17:17:57,265 - INFO - Executing solution for recaptcha_v2
2025-07-09 17:17:57,265 - INFO - Speaking: Solving recaptcha_v2 challenge
2025-07-09 17:18:00,916 - INFO - Speaking: Click the 'I'm not a robot' checkbox to initiate the reCAPTCHA challenge.
2025-07-09 17:18:06,938 - INFO - Speaking: Clicking I'm not a robot checkbox
2025-07-09 17:18:09,916 - INFO - Clicking at screenshot coords (71, 385) -> actual coords (71, 385) - I'm not a robot checkbox
2025-07-09 17:18:13,179 - INFO - Speaking: Waiting for verification
2025-07-09 17:18:22,442 - INFO - Challenge still present, may need additional steps
2025-07-09 17:18:24,444 - INFO - Attempt 2/3
2025-07-09 17:18:24,444 - INFO - Speaking: Attempt 2
2025-07-09 17:18:26,185 - INFO - Screenshot saved: screenshots\challenge_1752061706.png
2025-07-09 17:18:37,295 - INFO - Imported existing <module 'comtypes.gen' from '\\\\192.168.1.15\\user_data\\RUTVIK\\Documents\\Playground\\reCaptchaSolver\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-09 17:18:37,296 - INFO - Using writeable comtypes cache directory: '\\192.168.1.15\user_data\RUTVIK\Documents\Playground\reCaptchaSolver\.venv\Lib\site-packages\comtypes\gen'
2025-07-09 17:18:37,588 - INFO - Mouse cursor detected on monitor 2
2025-07-09 17:18:37,588 - INFO - Detected 2 monitors. Active monitor: 2
2025-07-09 17:18:37,588 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 17:18:37,588 - INFO - CaptchaSolver initialized
2025-07-09 17:18:37,588 - INFO - Active monitor: 2
2025-07-09 17:18:37,588 - INFO - Monitor offset: {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
2025-07-09 17:18:37,589 - INFO - Speaking: Captcha solver ready for multi-monitor setup
2025-07-09 17:18:41,261 - INFO - Monitor 1: 1920x1080 at (1920, -9)
2025-07-09 17:18:41,261 - INFO - Monitor 2: 1920x1080 at (0, 0)
2025-07-09 17:18:41,261 - INFO - Speaking: Found 2 monitors
2025-07-09 17:18:51,916 - INFO - Screenshot coords (71, 385) -> Actual coords (71, 385)
2025-07-09 17:18:51,916 - INFO - Speaking: Screenshot coordinates 71, 385 translate to actual coordinates 71, 385
