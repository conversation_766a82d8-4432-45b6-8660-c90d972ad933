from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    CO<PERSON><PERSON>H<PERSON>, Gray, IEnumVARIAN<PERSON>, O<PERSON>_XSIZE_CONTAINER,
    OLE_YPOS_CONTAINER, IFontDisp, OLE_YSIZE_CONTAINER,
    <PERSON>LE_CANCELBOOL, DISPMETHOD, dispid, FONTNAME, OLE_OPTEXCLUSIVE,
    BSTR, _lcid, OLE_ENABLEDEFAULTBOOL, CoClass, Library, GUID,
    OLE_HANDLE, OLE_YSIZE_HIMETRIC, FON<PERSON><PERSON>ERSCORE, IUnknown,
    Monochrome, Font, Color, OLE_YPOS_PIXELS, OLE_XSIZE_HIMETRIC,
    VgaColor, De<PERSON>ult, <PERSON><PERSON>_XPOS_CONTAINER, EXCEPINFO, IPictureDisp,
    FONT<PERSON>LD, IFontEventsDisp, Picture, _check_version, <PERSON><PERSON><PERSON>tch,
    VARIANT_BOOL, Std<PERSON>ont, Checked, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>THROUGH,
    OLE_XSIZE_PIXELS, OLE_YPOS_HIMETRIC, OLE_YSIZE_PIXELS,
    OLE_XPOS_HIMETRIC, IPicture, FontEvents, DISPPARAMS, FONTITALIC,
    FONTSIZE, OLE_COLOR, typelib_path, IFont, Unchecked,
    OLE_XPOS_PIXELS, DISPPROPERTY, HRESULT, StdPicture
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'OLE_XSIZE_HIMETRIC', 'VgaColor', 'Gray', 'Default',
    'OLE_XPOS_CONTAINER', 'OLE_XSIZE_CONTAINER', 'OLE_YPOS_CONTAINER',
    'LoadPictureConstants', 'IFontDisp', 'IPictureDisp',
    'OLE_YSIZE_CONTAINER', 'FONTBOLD', 'IFontEventsDisp', 'Picture',
    'OLE_TRISTATE', 'FONTNAME', 'OLE_OPTEXCLUSIVE',
    'OLE_ENABLEDEFAULTBOOL', 'StdFont', 'Checked',
    'FONTSTRIKETHROUGH', 'Library', 'OLE_HANDLE', 'OLE_YPOS_HIMETRIC',
    'OLE_YSIZE_PIXELS', 'OLE_XSIZE_PIXELS', 'OLE_XPOS_HIMETRIC',
    'OLE_YSIZE_HIMETRIC', 'IPicture', 'FontEvents', 'Color',
    'FONTITALIC', 'FONTUNDERSCORE', 'OLE_YPOS_PIXELS', 'FONTSIZE',
    'OLE_COLOR', 'Monochrome', 'typelib_path', 'Font', 'IFont',
    'Unchecked', 'OLE_XPOS_PIXELS', 'OLE_CANCELBOOL', 'StdPicture'
]

