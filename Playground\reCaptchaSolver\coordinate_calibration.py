#!/usr/bin/env python3
"""
Advanced Coordinate Calibration System
Ultra-precise coordinate mapping and calibration for multi-monitor setups
"""

import os
import sys
import time
import json
import math
import pyautogui
import mss
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import tkinter as tk
from tkinter import ttk, messagebox

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class CoordinateCalibrator:
    """Advanced coordinate calibration system"""
    
    def __init__(self):
        self.sct = mss.mss()
        self.monitors = self.sct.monitors
        self.calibration_points = []
        self.calibration_matrix = None
        self.active_monitor = 1
        self.calibration_data = {}
        
    def detect_optimal_monitor(self):
        """Detect the optimal monitor for captcha solving"""
        print("🔍 Detecting optimal monitor...")
        
        # Get current mouse position
        mouse_x, mouse_y = pyautogui.position()
        
        # Find monitor containing mouse
        for i, monitor in enumerate(self.monitors[1:], 1):
            if (monitor['left'] <= mouse_x < monitor['left'] + monitor['width'] and
                monitor['top'] <= mouse_y < monitor['top'] + monitor['height']):
                self.active_monitor = i
                print(f"✅ Active monitor detected: {i}")
                return i
        
        # Default to primary monitor
        self.active_monitor = 1
        return 1
    
    def create_calibration_grid(self, monitor_id=None):
        """Create precise calibration grid on target monitor"""
        if monitor_id is None:
            monitor_id = self.active_monitor
        
        monitor = self.monitors[monitor_id]
        print(f"📐 Creating calibration grid on monitor {monitor_id}")
        print(f"   Resolution: {monitor['width']}x{monitor['height']}")
        print(f"   Position: ({monitor['left']}, {monitor['top']})")
        
        # Capture current screenshot
        screenshot = self.sct.grab(monitor)
        img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
        
        # Create calibration overlay
        calibration_img = self.add_precision_grid(img)
        
        # Save calibration image
        timestamp = int(time.time())
        calibration_file = f"calibration_grid_{monitor_id}_{timestamp}.png"
        calibration_img.save(calibration_file)
        
        print(f"✅ Calibration grid saved: {calibration_file}")
        return calibration_img, calibration_file
    
    def add_precision_grid(self, img):
        """Add ultra-precise calibration grid to image"""
        grid_img = img.copy()
        draw = ImageDraw.Draw(grid_img)
        width, height = img.size
        
        try:
            font = ImageFont.truetype("arial.ttf", 10)
            large_font = ImageFont.truetype("arial.ttf", 14)
        except:
            font = ImageFont.load_default()
            large_font = ImageFont.load_default()
        
        # Grid parameters
        major_grid = 100  # Major grid lines every 100px
        minor_grid = 25   # Minor grid lines every 25px
        
        # Draw minor grid (light gray)
        for x in range(0, width, minor_grid):
            draw.line([(x, 0), (x, height)], fill="lightgray", width=1)
        for y in range(0, height, minor_grid):
            draw.line([(0, y), (width, y)], fill="lightgray", width=1)
        
        # Draw major grid (red)
        for x in range(0, width, major_grid):
            draw.line([(x, 0), (x, height)], fill="red", width=2)
            if x > 0:
                draw.text((x+2, 2), str(x), fill="red", font=font)
        
        for y in range(0, height, major_grid):
            draw.line([(0, y), (width, y)], fill="red", width=2)
            if y > 0:
                draw.text((2, y+2), str(y), fill="red", font=font)
        
        # Add calibration points (blue circles)
        calibration_points = [
            (50, 50, "TL"),
            (width//2, 50, "TC"),
            (width-50, 50, "TR"),
            (50, height//2, "ML"),
            (width//2, height//2, "CENTER"),
            (width-50, height//2, "MR"),
            (50, height-50, "BL"),
            (width//2, height-50, "BC"),
            (width-50, height-50, "BR")
        ]
        
        for x, y, label in calibration_points:
            # Draw circle
            radius = 15
            draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                        outline="blue", fill="lightblue", width=3)
            
            # Draw crosshair
            draw.line([(x-radius-5, y), (x+radius+5, y)], fill="blue", width=2)
            draw.line([(x, y-radius-5), (x, y+radius+5)], fill="blue", width=2)
            
            # Add coordinate label
            coord_text = f"({x},{y})"
            draw.text((x+20, y-10), coord_text, fill="blue", font=large_font)
            draw.text((x+20, y+5), label, fill="darkblue", font=font)
        
        # Add image info
        info_text = f"Image: {width}x{height} | Grid: {major_grid}px | Points: {len(calibration_points)}"
        draw.rectangle([10, height-40, len(info_text)*8, height-10], fill="black")
        draw.text((15, height-35), info_text, fill="white", font=font)
        
        return grid_img
    
    def interactive_calibration(self):
        """Interactive calibration with real-time feedback"""
        print("🎯 Starting interactive calibration...")
        
        # Create calibration window
        root = tk.Tk()
        root.title("Coordinate Calibration System")
        root.geometry("800x600")
        
        # Variables
        self.test_results = []
        
        # Create GUI
        ttk.Label(root, text="Advanced Coordinate Calibration", 
                 font=("Arial", 16, "bold")).pack(pady=10)
        
        # Monitor selection
        monitor_frame = ttk.Frame(root)
        monitor_frame.pack(pady=5)
        
        ttk.Label(monitor_frame, text="Target Monitor:").pack(side=tk.LEFT)
        monitor_var = tk.StringVar(value=str(self.active_monitor))
        monitor_combo = ttk.Combobox(monitor_frame, textvariable=monitor_var, width=10)
        monitor_combo['values'] = [str(i) for i in range(1, len(self.monitors))]
        monitor_combo.pack(side=tk.LEFT, padx=5)
        
        def update_monitor():
            self.active_monitor = int(monitor_var.get())
            status_var.set(f"Monitor {self.active_monitor} selected")
        
        ttk.Button(monitor_frame, text="Select", command=update_monitor).pack(side=tk.LEFT, padx=5)
        
        # Status
        status_var = tk.StringVar(value=f"Ready - Monitor {self.active_monitor}")
        ttk.Label(root, textvariable=status_var, font=("Arial", 10)).pack(pady=5)
        
        # Calibration controls
        cal_frame = ttk.LabelFrame(root, text="Calibration Controls", padding=10)
        cal_frame.pack(pady=10, padx=20, fill=tk.X)
        
        def create_grid():
            try:
                img, filename = self.create_calibration_grid(self.active_monitor)
                status_var.set(f"Calibration grid created: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create grid: {e}")
        
        ttk.Button(cal_frame, text="Create Calibration Grid", 
                  command=create_grid).pack(side=tk.LEFT, padx=5)
        
        # Test coordinates
        test_frame = ttk.LabelFrame(root, text="Coordinate Testing", padding=10)
        test_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        # Input fields
        input_frame = ttk.Frame(test_frame)
        input_frame.pack(pady=5)
        
        ttk.Label(input_frame, text="X:").pack(side=tk.LEFT)
        x_var = tk.StringVar()
        x_entry = ttk.Entry(input_frame, textvariable=x_var, width=8)
        x_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(input_frame, text="Y:").pack(side=tk.LEFT)
        y_var = tk.StringVar()
        y_entry = ttk.Entry(input_frame, textvariable=y_var, width=8)
        y_entry.pack(side=tk.LEFT, padx=2)
        
        def test_coordinate():
            try:
                x = int(x_var.get())
                y = int(y_var.get())
                
                # Test coordinate accuracy
                result = self.test_coordinate_precision(x, y, self.active_monitor)
                self.test_results.append(result)
                
                # Update results display
                results_text.insert(tk.END, f"Test {len(self.test_results)}: {result}\n")
                results_text.see(tk.END)
                
                status_var.set(f"Tested coordinate ({x}, {y}) - Error: {result['error']:.1f}px")
                
            except ValueError:
                messagebox.showerror("Error", "Invalid coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Test failed: {e}")
        
        ttk.Button(input_frame, text="Test Coordinate", 
                  command=test_coordinate).pack(side=tk.LEFT, padx=5)
        
        # Quick test buttons
        quick_frame = ttk.Frame(test_frame)
        quick_frame.pack(pady=5)
        
        quick_tests = [
            ("Center", lambda: self.quick_test_center()),
            ("Corners", lambda: self.quick_test_corners()),
            ("Grid Points", lambda: self.quick_test_grid())
        ]
        
        for label, command in quick_tests:
            ttk.Button(quick_frame, text=label, command=command).pack(side=tk.LEFT, padx=2)
        
        # Results display
        ttk.Label(test_frame, text="Test Results:").pack(anchor=tk.W, pady=(10,0))
        
        results_frame = ttk.Frame(test_frame)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        results_text = tk.Text(results_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_text.yview)
        results_text.config(yscrollcommand=scrollbar.set)
        
        results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Action buttons
        action_frame = ttk.Frame(root)
        action_frame.pack(pady=10)
        
        def save_calibration():
            if self.test_results:
                self.save_calibration_data()
                status_var.set("Calibration data saved")
            else:
                messagebox.showwarning("Warning", "No test results to save")
        
        def generate_report():
            if self.test_results:
                report = self.generate_accuracy_report()
                messagebox.showinfo("Accuracy Report", report)
            else:
                messagebox.showwarning("Warning", "No test results available")
        
        ttk.Button(action_frame, text="Save Calibration", 
                  command=save_calibration).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="Generate Report", 
                  command=generate_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="Close", 
                  command=root.destroy).pack(side=tk.LEFT, padx=5)
        
        root.mainloop()
    
    def test_coordinate_precision(self, screenshot_x, screenshot_y, monitor_id):
        """Test precision of coordinate translation"""
        monitor = self.monitors[monitor_id]
        
        # Calculate expected actual coordinates
        expected_x = screenshot_x + monitor['left']
        expected_y = screenshot_y + monitor['top']
        
        print(f"Testing coordinate precision:")
        print(f"  Screenshot: ({screenshot_x}, {screenshot_y})")
        print(f"  Expected actual: ({expected_x}, {expected_y})")
        
        # Move mouse to coordinate
        pyautogui.moveTo(expected_x, expected_y, duration=0.5)
        time.sleep(0.2)
        
        # Get actual mouse position
        actual_x, actual_y = pyautogui.position()
        
        # Calculate error
        error_x = abs(actual_x - expected_x)
        error_y = abs(actual_y - expected_y)
        total_error = math.sqrt(error_x**2 + error_y**2)
        
        result = {
            'screenshot_coords': (screenshot_x, screenshot_y),
            'expected_coords': (expected_x, expected_y),
            'actual_coords': (actual_x, actual_y),
            'error_x': error_x,
            'error_y': error_y,
            'error': total_error,
            'accuracy': max(0, 100 - total_error),
            'monitor': monitor_id
        }
        
        print(f"  Actual position: ({actual_x}, {actual_y})")
        print(f"  Error: {total_error:.1f}px")
        print(f"  Accuracy: {result['accuracy']:.1f}%")
        
        return result
    
    def quick_test_center(self):
        """Quick test of center coordinate"""
        monitor = self.monitors[self.active_monitor]
        center_x = monitor['width'] // 2
        center_y = monitor['height'] // 2
        return self.test_coordinate_precision(center_x, center_y, self.active_monitor)
    
    def quick_test_corners(self):
        """Quick test of corner coordinates"""
        monitor = self.monitors[self.active_monitor]
        corners = [
            (50, 50),
            (monitor['width']-50, 50),
            (50, monitor['height']-50),
            (monitor['width']-50, monitor['height']-50)
        ]
        
        results = []
        for x, y in corners:
            result = self.test_coordinate_precision(x, y, self.active_monitor)
            results.append(result)
            time.sleep(1)
        
        return results
    
    def quick_test_grid(self):
        """Quick test of grid points"""
        monitor = self.monitors[self.active_monitor]
        grid_points = []
        
        # Create 3x3 grid
        for i in range(3):
            for j in range(3):
                x = (monitor['width'] * (i + 1)) // 4
                y = (monitor['height'] * (j + 1)) // 4
                grid_points.append((x, y))
        
        results = []
        for x, y in grid_points:
            result = self.test_coordinate_precision(x, y, self.active_monitor)
            results.append(result)
            time.sleep(0.5)
        
        return results
    
    def save_calibration_data(self):
        """Save calibration data to file"""
        calibration_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'active_monitor': self.active_monitor,
            'monitor_info': self.monitors[self.active_monitor],
            'test_results': self.test_results,
            'accuracy_stats': self.calculate_accuracy_stats()
        }
        
        filename = f"calibration_data_{self.active_monitor}_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(calibration_data, f, indent=2)
        
        print(f"✅ Calibration data saved: {filename}")
        return filename
    
    def calculate_accuracy_stats(self):
        """Calculate accuracy statistics"""
        if not self.test_results:
            return {}
        
        errors = [result['error'] for result in self.test_results]
        accuracies = [result['accuracy'] for result in self.test_results]
        
        return {
            'total_tests': len(self.test_results),
            'average_error': sum(errors) / len(errors),
            'max_error': max(errors),
            'min_error': min(errors),
            'average_accuracy': sum(accuracies) / len(accuracies),
            'tests_under_5px': len([e for e in errors if e < 5]),
            'tests_under_10px': len([e for e in errors if e < 10])
        }
    
    def generate_accuracy_report(self):
        """Generate detailed accuracy report"""
        stats = self.calculate_accuracy_stats()
        
        report = f"""
COORDINATE ACCURACY REPORT
========================

Monitor: {self.active_monitor}
Total Tests: {stats['total_tests']}

ACCURACY METRICS:
- Average Error: {stats['average_error']:.1f} pixels
- Maximum Error: {stats['max_error']:.1f} pixels  
- Minimum Error: {stats['min_error']:.1f} pixels
- Average Accuracy: {stats['average_accuracy']:.1f}%

PRECISION ANALYSIS:
- Tests under 5px error: {stats['tests_under_5px']}/{stats['total_tests']} ({stats['tests_under_5px']/stats['total_tests']*100:.1f}%)
- Tests under 10px error: {stats['tests_under_10px']}/{stats['total_tests']} ({stats['tests_under_10px']/stats['total_tests']*100:.1f}%)

RECOMMENDATION:
{'✅ Excellent accuracy - ready for production' if stats['average_error'] < 5 else 
 '⚠️  Moderate accuracy - consider recalibration' if stats['average_error'] < 10 else
 '❌ Poor accuracy - recalibration required'}
        """
        
        return report

def main():
    """Main calibration interface"""
    calibrator = CoordinateCalibrator()
    
    print("🎯 Advanced Coordinate Calibration System")
    print("=" * 50)
    
    # Detect optimal monitor
    calibrator.detect_optimal_monitor()
    
    # Start interactive calibration
    calibrator.interactive_calibration()

if __name__ == "__main__":
    main()
