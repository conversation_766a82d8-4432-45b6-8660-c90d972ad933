#!/usr/bin/env python3
"""
Example Usage Script for Coordinate-based Web Browser Agent AI
Demonstrates different ways to use the solver for various scenarios
"""

import os
import time
from solver import CaptchaSolver, ClickCoordinate

def example_basic_usage():
    """Example 1: Basic challenge solving"""
    print("=== Example 1: Basic Challenge Solving ===")
    
    # Load API key
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY in your .env file")
        return
    
    # Initialize solver
    solver = CaptchaSolver(api_key)
    
    # Solve current challenge on screen
    print("Analyzing current screen for challenges...")
    success = solver.solve_challenge()
    
    if success:
        print("✅ Challenge solved successfully!")
    else:
        print("❌ Failed to solve challenge")

def example_cloudflare_specific():
    """Example 2: Cloudflare-specific solving"""
    print("\n=== Example 2: Cloudflare Challenge ===")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY in your .env file")
        return
    
    solver = CaptchaSolver(api_key)
    
    # Navigate to a page with Cloudflare protection (example)
    print("Solving Cloudflare challenge...")
    success = solver.solve_cloudflare()
    
    if success:
        print("✅ Cloudflare challenge bypassed!")
    else:
        print("❌ Cloudflare challenge failed")

def example_recaptcha_specific():
    """Example 3: reCAPTCHA-specific solving"""
    print("\n=== Example 3: reCAPTCHA Challenge ===")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY in your .env file")
        return
    
    solver = CaptchaSolver(api_key)
    
    # Solve reCAPTCHA challenge
    print("Solving reCAPTCHA challenge...")
    success = solver.solve_recaptcha()
    
    if success:
        print("✅ reCAPTCHA solved!")
    else:
        print("❌ reCAPTCHA solving failed")

def example_continuous_monitoring():
    """Example 4: Continuous monitoring mode"""
    print("\n=== Example 4: Continuous Monitoring ===")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY in your .env file")
        return
    
    solver = CaptchaSolver(api_key)
    
    print("Starting continuous monitoring...")
    print("The solver will automatically detect and solve challenges")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        # Monitor every 3 seconds
        solver.continuous_monitoring(interval=3)
    except KeyboardInterrupt:
        print("\n✅ Monitoring stopped by user")

def example_custom_configuration():
    """Example 5: Custom solver configuration"""
    print("\n=== Example 5: Custom Configuration ===")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY in your .env file")
        return
    
    # Initialize solver with custom settings
    solver = CaptchaSolver(api_key)
    
    # Customize solver behavior
    solver.max_attempts = 5  # Try up to 5 times
    solver.confidence_threshold = 0.8  # Higher confidence required
    solver.wait_timeout = 45  # Wait longer for responses
    
    # Customize text-to-speech
    solver.tts.engine.setProperty('rate', 120)  # Slower speech
    solver.tts.engine.setProperty('volume', 1.0)  # Maximum volume
    
    print("Solver configured with custom settings:")
    print(f"- Max attempts: {solver.max_attempts}")
    print(f"- Confidence threshold: {solver.confidence_threshold}")
    print(f"- Wait timeout: {solver.wait_timeout}s")
    
    # Test with custom configuration
    success = solver.solve_challenge()
    print(f"Result with custom config: {'Success' if success else 'Failed'}")

def example_manual_coordinate_clicking():
    """Example 6: Manual coordinate clicking"""
    print("\n=== Example 6: Manual Coordinate Clicking ===")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY in your .env file")
        return
    
    solver = CaptchaSolver(api_key)
    
    # Create custom click coordinates
    coordinates = [
        ClickCoordinate(
            x=500, y=300,
            confidence=0.95,
            description="Verification checkbox",
            element_type="checkbox"
        ),
        ClickCoordinate(
            x=600, y=450,
            confidence=0.90,
            description="Submit button",
            element_type="button"
        )
    ]
    
    print("Executing manual coordinate sequence...")
    for coord in coordinates:
        print(f"Clicking: {coord.description} at ({coord.x}, {coord.y})")
        success = solver.automation.click_coordinate(coord)
        if success:
            print(f"✅ Successfully clicked {coord.description}")
        else:
            print(f"❌ Failed to click {coord.description}")
        time.sleep(1)

def example_screenshot_analysis():
    """Example 7: Screenshot analysis without clicking"""
    print("\n=== Example 7: Screenshot Analysis Only ===")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY in your .env file")
        return
    
    solver = CaptchaSolver(api_key)
    
    # Capture and analyze screenshot
    print("Capturing and analyzing current screen...")
    screenshot = solver.screen_capture.capture_screen()
    
    # Save screenshot
    timestamp = int(time.time())
    filename = f"analysis_{timestamp}.png"
    filepath = solver.screen_capture.save_screenshot(screenshot, filename)
    print(f"Screenshot saved: {filepath}")
    
    # Analyze with Gemini
    analysis = solver.gemini.analyze_challenge(screenshot)
    
    print(f"\nAnalysis Results:")
    print(f"Challenge Type: {analysis.challenge_type}")
    print(f"Instructions: {analysis.instructions}")
    print(f"Success Probability: {analysis.success_probability:.2%}")
    print(f"Found {len(analysis.click_coordinates)} clickable elements:")
    
    for i, coord in enumerate(analysis.click_coordinates, 1):
        print(f"  {i}. {coord.description} at ({coord.x}, {coord.y}) "
              f"[Confidence: {coord.confidence:.2%}]")
    
    print(f"Next Steps: {', '.join(analysis.next_steps)}")

def example_accessibility_features():
    """Example 8: Accessibility features demonstration"""
    print("\n=== Example 8: Accessibility Features ===")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY in your .env file")
        return
    
    solver = CaptchaSolver(api_key)
    
    # Demonstrate TTS features
    print("Testing text-to-speech features...")
    
    messages = [
        "Welcome to the accessibility demonstration",
        "This system provides audio feedback for all operations",
        "Challenge detection will be announced",
        "Step by step instructions will be spoken",
        "Success and failure status will be reported"
    ]
    
    for message in messages:
        print(f"Speaking: {message}")
        solver.tts.speak(message, wait=True)
        time.sleep(0.5)
    
    print("✅ Accessibility features demonstrated")

def main():
    """Main function to run examples"""
    print("🤖 Coordinate-based Web Browser Agent AI - Examples")
    print("=" * 60)
    
    examples = {
        "1": ("Basic Challenge Solving", example_basic_usage),
        "2": ("Cloudflare Specific", example_cloudflare_specific),
        "3": ("reCAPTCHA Specific", example_recaptcha_specific),
        "4": ("Continuous Monitoring", example_continuous_monitoring),
        "5": ("Custom Configuration", example_custom_configuration),
        "6": ("Manual Coordinates", example_manual_coordinate_clicking),
        "7": ("Screenshot Analysis", example_screenshot_analysis),
        "8": ("Accessibility Features", example_accessibility_features),
        "9": ("Run All Examples", None)
    }
    
    print("\nAvailable Examples:")
    for key, (description, _) in examples.items():
        print(f"{key}. {description}")
    
    try:
        choice = input("\nEnter example number (1-9): ").strip()
        
        if choice == "9":
            # Run all examples
            for key, (description, func) in examples.items():
                if func and key != "4":  # Skip continuous monitoring in batch
                    print(f"\n{'='*20} Running Example {key} {'='*20}")
                    func()
                    time.sleep(2)
        elif choice in examples and examples[choice][1]:
            examples[choice][1]()
        else:
            print("Invalid choice or example not available")
            
    except KeyboardInterrupt:
        print("\n\nExamples interrupted by user")
    except Exception as e:
        print(f"\nError running example: {e}")

if __name__ == "__main__":
    main()
