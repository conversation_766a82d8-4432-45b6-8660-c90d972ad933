#!/usr/bin/env python3
"""
Coordinate-based Web Browser Agent AI for Cloudflare and reCAPTCHA Solver
Designed for visually impaired employees using Gemini 2.5 Flash

This module provides an intelligent web browser automation system that:
1. Takes screenshots of web pages
2. Uses Gemini 2.5 Flash to analyze challenges
3. Provides coordinates for clicking elements
4. Solves Cloudflare and reCAPTCHA challenges
5. Provides audio feedback for accessibility
"""

import os
import time
import json
import base64
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from pathlib import Path

import pyautogui
import pyttsx3
import mss
import requests
import google.generativeai as genai
from PIL import Image, ImageDraw
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('solver.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ClickCoordinate:
    """Represents a coordinate to click with confidence score"""
    x: int
    y: int
    confidence: float
    description: str
    element_type: str  # 'button', 'checkbox', 'image', 'text'

@dataclass
class ChallengeAnalysis:
    """Analysis result from Gemini AI"""
    challenge_type: str  # 'cloudflare', 'recaptcha_v2', 'recaptcha_v3', 'hcaptcha'
    instructions: str
    click_coordinates: List[ClickCoordinate]
    success_probability: float
    next_steps: List[str]

class TextToSpeechManager:
    """Manages text-to-speech functionality for accessibility"""
    
    def __init__(self):
        self.engine = pyttsx3.init()
        self.engine.setProperty('rate', 150)  # Adjust speech rate
        self.engine.setProperty('volume', 0.8)
    
    def speak(self, text: str, wait: bool = False):
        """Convert text to speech"""
        try:
            logger.info(f"Speaking: {text}")
            self.engine.say(text)
            if wait:
                self.engine.runAndWait()
            else:
                # Non-blocking speech
                self.engine.startLoop(False)
                self.engine.iterate()
                self.engine.endLoop()
        except Exception as e:
            logger.error(f"TTS error: {e}")

class ScreenCapture:
    """Handles screen capture and image processing"""
    
    def __init__(self):
        self.sct = mss.mss()
    
    def capture_screen(self, region: Optional[Dict] = None) -> Image.Image:
        """Capture screen or specific region"""
        try:
            if region:
                screenshot = self.sct.grab(region)
            else:
                screenshot = self.sct.grab(self.sct.monitors[1])  # Primary monitor
            
            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return img
        except Exception as e:
            logger.error(f"Screen capture error: {e}")
            raise
    
    def save_screenshot(self, img: Image.Image, filename: str) -> str:
        """Save screenshot to file"""
        filepath = Path(f"screenshots/{filename}")
        filepath.parent.mkdir(exist_ok=True)
        img.save(filepath)
        logger.info(f"Screenshot saved: {filepath}")
        return str(filepath)
    
    def encode_image_base64(self, img: Image.Image) -> str:
        """Encode image to base64 for API"""
        import io
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        return base64.b64encode(buffer.getvalue()).decode()

class GeminiAnalyzer:
    """Gemini 2.5 Flash AI analyzer for challenge detection and solving"""
    
    def __init__(self, api_key: str):
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
    
    def analyze_challenge(self, image: Image.Image) -> ChallengeAnalysis:
        """Analyze screenshot to identify and solve challenges"""
        try:
            # Prepare the prompt for challenge analysis
            prompt = self._create_analysis_prompt()
            
            # Send image to Gemini
            response = self.model.generate_content([prompt, image])
            
            # Parse response
            analysis = self._parse_gemini_response(response.text)
            return analysis
            
        except Exception as e:
            logger.error(f"Gemini analysis error: {e}")
            raise
    
    def _create_analysis_prompt(self) -> str:
        """Create detailed prompt for Gemini analysis"""
        return """
        You are an AI assistant helping visually impaired users navigate web challenges.
        Analyze this screenshot and identify any of the following:
        
        1. Cloudflare security challenges
        2. reCAPTCHA v2 (image selection)
        3. reCAPTCHA v3 (invisible)
        4. hCaptcha challenges
        5. Other bot detection systems
        
        For each challenge found, provide:
        - Challenge type
        - Exact pixel coordinates to click (x, y)
        - Confidence level (0-1)
        - Step-by-step instructions
        - Description of what to click
        
        Return response in JSON format:
        {
            "challenge_type": "cloudflare|recaptcha_v2|recaptcha_v3|hcaptcha|none",
            "instructions": "Clear step-by-step instructions",
            "click_coordinates": [
                {
                    "x": 123,
                    "y": 456,
                    "confidence": 0.95,
                    "description": "Cloudflare verification button",
                    "element_type": "button"
                }
            ],
            "success_probability": 0.85,
            "next_steps": ["Wait for verification", "Check for redirect"]
        }
        
        Be precise with coordinates and provide accessibility-friendly descriptions.
        """
    
    def _parse_gemini_response(self, response_text: str) -> ChallengeAnalysis:
        """Parse Gemini response into structured data"""
        try:
            # Extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if not json_match:
                raise ValueError("No JSON found in response")
            
            data = json.loads(json_match.group())
            
            # Convert to ClickCoordinate objects
            coordinates = []
            for coord_data in data.get('click_coordinates', []):
                coordinates.append(ClickCoordinate(
                    x=coord_data['x'],
                    y=coord_data['y'],
                    confidence=coord_data['confidence'],
                    description=coord_data['description'],
                    element_type=coord_data['element_type']
                ))
            
            return ChallengeAnalysis(
                challenge_type=data['challenge_type'],
                instructions=data['instructions'],
                click_coordinates=coordinates,
                success_probability=data['success_probability'],
                next_steps=data['next_steps']
            )
            
        except Exception as e:
            logger.error(f"Response parsing error: {e}")
            # Return default analysis
            return ChallengeAnalysis(
                challenge_type="unknown",
                instructions="Could not analyze challenge",
                click_coordinates=[],
                success_probability=0.0,
                next_steps=["Manual intervention required"]
            )

class AutomationController:
    """Controls mouse and keyboard automation"""
    
    def __init__(self):
        # Configure PyAutoGUI
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
    
    def click_coordinate(self, coord: ClickCoordinate) -> bool:
        """Click at specified coordinate with safety checks"""
        try:
            logger.info(f"Clicking at ({coord.x}, {coord.y}) - {coord.description}")
            
            # Move to coordinate smoothly
            pyautogui.moveTo(coord.x, coord.y, duration=0.5)
            time.sleep(0.2)
            
            # Click
            pyautogui.click(coord.x, coord.y)
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            logger.error(f"Click error: {e}")
            return False
    
    def wait_for_element_change(self, region: Dict, timeout: int = 10) -> bool:
        """Wait for visual changes in a region"""
        try:
            initial_screenshot = pyautogui.screenshot(region=tuple(region.values()))
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                current_screenshot = pyautogui.screenshot(region=tuple(region.values()))
                if not self._images_similar(initial_screenshot, current_screenshot):
                    return True
                time.sleep(0.5)
            
            return False
            
        except Exception as e:
            logger.error(f"Wait for change error: {e}")
            return False
    
    def _images_similar(self, img1: Image.Image, img2: Image.Image, threshold: float = 0.95) -> bool:
        """Compare two images for similarity"""
        try:
            # Convert to numpy arrays
            arr1 = np.array(img1)
            arr2 = np.array(img2)
            
            # Calculate similarity
            diff = np.abs(arr1 - arr2)
            similarity = 1 - (np.sum(diff) / (arr1.size * 255))
            
            return similarity > threshold
            
        except Exception as e:
            logger.error(f"Image comparison error: {e}")
            return False

class CaptchaSolver:
    """Main solver class that coordinates all components"""

    def __init__(self, gemini_api_key: str):
        self.tts = TextToSpeechManager()
        self.screen_capture = ScreenCapture()
        self.gemini = GeminiAnalyzer(gemini_api_key)
        self.automation = AutomationController()

        # Configuration
        self.max_attempts = 3
        self.wait_timeout = 30
        self.confidence_threshold = 0.7

        logger.info("CaptchaSolver initialized")
        self.tts.speak("Captcha solver ready", wait=True)

    def solve_challenge(self, url: Optional[str] = None) -> bool:
        """
        Main method to solve challenges

        Step-by-step procedure:
        1. Capture current screen
        2. Analyze with Gemini AI
        3. Execute clicking sequence
        4. Verify success
        5. Provide feedback
        """
        try:
            self.tts.speak("Starting challenge analysis")

            for attempt in range(self.max_attempts):
                logger.info(f"Attempt {attempt + 1}/{self.max_attempts}")
                self.tts.speak(f"Attempt {attempt + 1}")

                # Step 1: Capture screen
                screenshot = self._capture_and_save_screenshot()

                # Step 2: Analyze with Gemini
                analysis = self.gemini.analyze_challenge(screenshot)

                # Step 3: Process analysis
                if analysis.challenge_type == "none":
                    self.tts.speak("No challenge detected")
                    return True

                # Step 4: Execute solution
                success = self._execute_solution(analysis)

                if success:
                    self.tts.speak("Challenge solved successfully")
                    return True

                # Wait before retry
                time.sleep(2)

            self.tts.speak("Failed to solve challenge after maximum attempts")
            return False

        except Exception as e:
            logger.error(f"Solve challenge error: {e}")
            self.tts.speak("Error occurred during challenge solving")
            return False

    def _capture_and_save_screenshot(self) -> Image.Image:
        """Capture and save screenshot for analysis"""
        screenshot = self.screen_capture.capture_screen()
        timestamp = int(time.time())
        filename = f"challenge_{timestamp}.png"
        self.screen_capture.save_screenshot(screenshot, filename)
        return screenshot

    def _execute_solution(self, analysis: ChallengeAnalysis) -> bool:
        """Execute the solution based on analysis"""
        try:
            logger.info(f"Executing solution for {analysis.challenge_type}")
            self.tts.speak(f"Solving {analysis.challenge_type} challenge")

            # Provide instructions to user
            self.tts.speak(analysis.instructions)

            # Execute clicks in sequence
            for coord in analysis.click_coordinates:
                if coord.confidence < self.confidence_threshold:
                    logger.warning(f"Low confidence click: {coord.confidence}")
                    continue

                self.tts.speak(f"Clicking {coord.description}")
                success = self.automation.click_coordinate(coord)

                if not success:
                    return False

                # Wait for response
                time.sleep(1)

            # Wait for challenge completion
            return self._wait_for_completion(analysis)

        except Exception as e:
            logger.error(f"Solution execution error: {e}")
            return False

    def _wait_for_completion(self, analysis: ChallengeAnalysis) -> bool:
        """Wait for challenge completion and verify success"""
        try:
            self.tts.speak("Waiting for verification")

            # Wait for page changes
            time.sleep(3)

            # Take new screenshot to verify
            new_screenshot = self.screen_capture.capture_screen()
            new_analysis = self.gemini.analyze_challenge(new_screenshot)

            # Check if challenge is resolved
            if new_analysis.challenge_type == "none":
                return True

            # If same challenge type, might need more interaction
            if new_analysis.challenge_type == analysis.challenge_type:
                logger.info("Challenge still present, may need additional steps")
                return False

            return True

        except Exception as e:
            logger.error(f"Completion verification error: {e}")
            return False

    def solve_cloudflare(self) -> bool:
        """Specialized method for Cloudflare challenges"""
        self.tts.speak("Detecting Cloudflare challenge")
        return self.solve_challenge()

    def solve_recaptcha(self) -> bool:
        """Specialized method for reCAPTCHA challenges"""
        self.tts.speak("Detecting reCAPTCHA challenge")
        return self.solve_challenge()

    def continuous_monitoring(self, interval: int = 5):
        """Continuously monitor for challenges"""
        self.tts.speak("Starting continuous monitoring mode")

        try:
            while True:
                screenshot = self.screen_capture.capture_screen()
                analysis = self.gemini.analyze_challenge(screenshot)

                if analysis.challenge_type != "none":
                    logger.info(f"Challenge detected: {analysis.challenge_type}")
                    self.solve_challenge()

                time.sleep(interval)

        except KeyboardInterrupt:
            self.tts.speak("Monitoring stopped")
            logger.info("Continuous monitoring stopped")

def main():
    """
    Main function with step-by-step procedure

    Usage Examples:
    1. Basic challenge solving
    2. Continuous monitoring
    3. Specific challenge types
    """

    # Step 1: Load configuration
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Error: GEMINI_API_KEY not found in environment variables")
        print("Please set your Gemini API key in .env file")
        return

    # Step 2: Initialize solver
    try:
        solver = CaptchaSolver(api_key)
    except Exception as e:
        print(f"Failed to initialize solver: {e}")
        return

    # Step 3: Choose operation mode
    print("\nCaptcha Solver - Choose operation mode:")
    print("1. Solve current challenge")
    print("2. Continuous monitoring")
    print("3. Solve Cloudflare specifically")
    print("4. Solve reCAPTCHA specifically")

    try:
        choice = input("Enter choice (1-4): ").strip()

        if choice == "1":
            success = solver.solve_challenge()
            print(f"Challenge solving {'successful' if success else 'failed'}")

        elif choice == "2":
            print("Starting continuous monitoring (Press Ctrl+C to stop)")
            solver.continuous_monitoring()

        elif choice == "3":
            success = solver.solve_cloudflare()
            print(f"Cloudflare challenge {'solved' if success else 'failed'}")

        elif choice == "4":
            success = solver.solve_recaptcha()
            print(f"reCAPTCHA challenge {'solved' if success else 'failed'}")

        else:
            print("Invalid choice")

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        print(f"Error during operation: {e}")

if __name__ == "__main__":
    main()
