#!/usr/bin/env python3
"""
Coordinate-based Web Browser Agent AI for Cloudflare and reCAPTCHA Solver
Designed for visually impaired employees using Gemini 2.5 Flash

This module provides an intelligent web browser automation system that:
1. Takes screenshots of web pages
2. Uses Gemini 2.5 Flash to analyze challenges
3. Provides coordinates for clicking elements
4. Solves Cloudflare and reCAPTC<PERSON> challenges
5. Provides audio feedback for accessibility
"""

import os
import sys
import time
import json
import base64
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from pathlib import Path

import pyautogui
import pyttsx3
import mss
import requests
import google.generativeai as genai
from PIL import Image, ImageDraw
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding="utf-8",    # crucial for unicode
    handlers=[
        logging.FileHandler('solver.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

import ctypes
ctypes.windll.user32.SetProcessDPIAware()

@dataclass
class ClickCoordinate:
    """Represents a coordinate to click with confidence score"""
    x: int
    y: int
    confidence: float
    description: str
    element_type: str  # 'button', 'checkbox', 'image', 'text'

@dataclass
class ChallengeAnalysis:
    """Analysis result from Gemini AI"""
    challenge_type: str  # 'cloudflare', 'recaptcha_v2', 'recaptcha_v3', 'hcaptcha'
    instructions: str
    click_coordinates: List[ClickCoordinate]
    success_probability: float
    next_steps: List[str]

class TextToSpeechManager:
    """Manages text-to-speech functionality for accessibility"""
    
    def __init__(self):
        self.engine = pyttsx3.init()
        self.engine.setProperty('rate', 150)  # Adjust speech rate
        self.engine.setProperty('volume', 0.8)
    
    def speak(self, text: str, wait: bool = False):
        """Convert text to speech"""
        try:
            logger.info(f"Speaking: {text}")
            self.engine.say(text)
            if wait:
                self.engine.runAndWait()
            else:
                # Non-blocking speech
                self.engine.startLoop(False)
                self.engine.iterate()
                self.engine.endLoop()
        except Exception as e:
            logger.error(f"TTS error: {e}")

class ScreenCapture:
    """Handles screen capture and image processing with multi-monitor support"""

    def __init__(self):
        self.sct = mss.mss()
        self.monitors = self.sct.monitors
        self.active_monitor = self._detect_active_monitor()
        self.monitor_offset = self._get_monitor_offset()
        logger.info(f"Detected {len(self.monitors)-1} monitors. Active monitor: {self.active_monitor}")
        logger.info(f"Monitor offset: {self.monitor_offset}")

    def _detect_active_monitor(self) -> int:
        """Detect which monitor has the active window or cursor"""
        try:
            import pyautogui
            # Get current mouse position
            mouse_x, mouse_y = pyautogui.position()

            # Check which monitor contains the mouse cursor
            for i, monitor in enumerate(self.monitors[1:], 1):  # Skip index 0 (all monitors)
                if (monitor['left'] <= mouse_x < monitor['left'] + monitor['width'] and
                    monitor['top'] <= mouse_y < monitor['top'] + monitor['height']):
                    logger.info(f"Mouse cursor detected on monitor {i}")
                    return i

            # Default to primary monitor if cursor not found
            return 1
        except Exception as e:
            logger.warning(f"Could not detect active monitor: {e}")
            return 1

    def _get_monitor_offset(self) -> Dict[str, int]:
        """Get the offset of the active monitor"""
        if self.active_monitor < len(self.monitors):
            monitor = self.monitors[self.active_monitor]
            return {
                'x': monitor['left'],
                'y': monitor['top'],
                'width': monitor['width'],
                'height': monitor['height']
            }
        return {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}

    def capture_screen(self, region: Optional[Dict] = None, monitor_id: Optional[int] = None) -> Image.Image:
        """Capture screen or specific region from specified monitor"""
        try:
            target_monitor = monitor_id or self.active_monitor

            if region:
                # Adjust region coordinates for monitor offset
                adjusted_region = {
                    'left': region['left'] + self.monitor_offset['x'],
                    'top': region['top'] + self.monitor_offset['y'],
                    'width': region['width'],
                    'height': region['height']
                }
                screenshot = self.sct.grab(adjusted_region)
            else:
                # Capture the active monitor
                screenshot = self.sct.grab(self.monitors[target_monitor])

            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return img
        except Exception as e:
            logger.error(f"Screen capture error: {e}")
            raise
    
    def save_screenshot(self, img: Image.Image, filename: str) -> str:
        """Save screenshot to file"""
        filepath = Path(f"screenshots/{filename}")
        filepath.parent.mkdir(exist_ok=True)
        img.save(filepath)
        logger.info(f"Screenshot saved: {filepath}")
        return str(filepath)
    
    def encode_image_base64(self, img: Image.Image) -> str:
        """Encode image to base64 for API"""
        import io
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        return base64.b64encode(buffer.getvalue()).decode()

class GeminiAnalyzer:
    """Gemini 2.5 Flash AI analyzer for challenge detection and solving"""
    
    def __init__(self, api_key: str):
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
    
    def analyze_challenge(self, image: Image.Image) -> ChallengeAnalysis:
        """Analyze screenshot to identify and solve challenges"""
        try:
            # Get image dimensions
            image_width, image_height = image.size
            logger.info(f"Analyzing image: {image_width}x{image_height}")

            # Prepare the prompt for challenge analysis
            prompt = self._create_analysis_prompt(image_width, image_height)

            # Send image to Gemini
            response = self.model.generate_content([prompt, image])

            # Parse response and validate coordinates
            analysis = self._parse_gemini_response(response.text, image_width, image_height)
            return analysis

        except Exception as e:
            logger.error(f"Gemini analysis error: {e}")
            raise
    
    def _create_analysis_prompt(self, image_width: int, image_height: int) -> str:
        """Create ultra-precise prompt for Gemini analysis with enhanced coordinate accuracy"""
        return f"""
        You are an expert computer vision AI analyzing web screenshots for precise coordinate detection.

        CRITICAL COORDINATE REQUIREMENTS:
        - Image dimensions: {image_width} x {image_height} pixels
        - Coordinate system: TOP-LEFT corner is (0,0)
        - Valid X range: 0 to {image_width-1}
        - Valid Y range: 0 to {image_height-1}
        - ALL coordinates MUST be within these bounds

        ANALYSIS TASK:
        Identify clickable elements for these challenge types:
        1. Cloudflare "Checking your browser" - look for verification checkboxes/buttons
        2. reCAPTCHA v2 "I'm not a robot" - checkbox or image grid selection
        3. reCAPTCHA v3 - invisible badges or verification elements
        4. hCaptcha - similar to reCAPTCHA with different branding
        5. Custom bot detection - any verification buttons or checkboxes

        COORDINATE PRECISION PROTOCOL:
        1. Locate the EXACT CENTER of clickable elements
        2. For checkboxes: center of the checkbox square
        3. For buttons: center of the button area
        4. For text links: center of the clickable text
        5. Measure pixel-perfect coordinates using the image grid
        6. Verify coordinates are within bounds before reporting

        ENHANCED ACCURACY REQUIREMENTS:
        - Provide bounding box coordinates (left, top, right, bottom)
        - Calculate center coordinates from bounding box
        - Include element dimensions for validation
        - Report confidence based on visual clarity
        - Flag any coordinate uncertainty

        RESPONSE FORMAT (JSON):
        {{
            "challenge_type": "cloudflare|recaptcha_v2|recaptcha_v3|hcaptcha|custom|none",
            "instructions": "Detailed step-by-step clicking instructions",
            "image_analysis": {{
                "width": {image_width},
                "height": {image_height},
                "detected_elements": "number of clickable elements found"
            }},
            "click_coordinates": [
                {{
                    "x": 123,
                    "y": 456,
                    "confidence": 0.95,
                    "description": "Cloudflare verification checkbox",
                    "element_type": "checkbox|button|link|image",
                    "bounding_box": {{
                        "left": 100,
                        "top": 440,
                        "right": 146,
                        "bottom": 472,
                        "width": 46,
                        "height": 32
                    }},
                    "center_calculation": "Center of bounding box: ((100+146)/2, (440+472)/2) = (123, 456)",
                    "validation": "Coordinates within bounds: x(123) < {image_width}, y(456) < {image_height}",
                    "visual_features": "Square checkbox with border, clearly visible"
                }}
            ],
            "coordinate_accuracy": {{
                "method": "Bounding box center calculation",
                "precision_level": "pixel-perfect",
                "validation_passed": true
            }},
            "success_probability": 0.95,
            "next_steps": ["Click element", "Wait for response", "Verify completion"]
        }}

        CRITICAL: Double-check ALL coordinates before responding. Invalid coordinates will cause system failure.
        """
    
    def _parse_gemini_response(self, response_text: str, image_width: int, image_height: int) -> ChallengeAnalysis:
        """Parse Gemini response with enhanced coordinate validation and accuracy"""
        try:
            # Extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if not json_match:
                raise ValueError("No JSON found in response")

            data = json.loads(json_match.group())
            logger.info(f"=== GEMINI RESPONSE ANALYSIS ===")
            logger.info(f"Challenge type: {data.get('challenge_type', 'unknown')}")
            logger.info(f"Image dimensions: {image_width}x{image_height}")

            # Enhanced coordinate processing with multiple validation layers
            coordinates = []
            for i, coord_data in enumerate(data.get('click_coordinates', [])):
                logger.info(f"Processing coordinate {i+1}:")

                # Extract coordinate data with fallbacks
                x = coord_data.get('x', 0)
                y = coord_data.get('y', 0)
                confidence = coord_data.get('confidence', 0.5)
                description = coord_data.get('description', f'Element {i+1}')
                element_type = coord_data.get('element_type', 'unknown')

                logger.info(f"  Raw coordinates: ({x}, {y})")
                logger.info(f"  Element: {description}")
                logger.info(f"  Type: {element_type}")
                logger.info(f"  Confidence: {confidence:.2%}")

                # Enhanced coordinate validation
                original_x, original_y = x, y
                coordinate_valid = True

                # Check bounds
                if not (0 <= x < image_width and 0 <= y < image_height):
                    logger.warning(f"  ❌ Coordinates out of bounds!")
                    logger.warning(f"     Valid range: X(0-{image_width-1}), Y(0-{image_height-1})")

                    # Smart clamping with margin
                    margin = 10
                    x = max(margin, min(x, image_width - margin))
                    y = max(margin, min(y, image_height - margin))

                    logger.info(f"  🔧 Clamped to: ({x}, {y})")
                    confidence *= 0.7  # Reduce confidence for clamped coordinates
                    coordinate_valid = False

                # Validate against bounding box if provided
                if 'bounding_box' in coord_data:
                    bbox = coord_data['bounding_box']
                    bbox_center_x = (bbox.get('left', 0) + bbox.get('right', 0)) // 2
                    bbox_center_y = (bbox.get('top', 0) + bbox.get('bottom', 0)) // 2

                    logger.info(f"  📦 Bounding box center: ({bbox_center_x}, {bbox_center_y})")

                    # Use bounding box center if coordinates don't match
                    if abs(x - bbox_center_x) > 5 or abs(y - bbox_center_y) > 5:
                        logger.info(f"  🔧 Using bounding box center instead")
                        x, y = bbox_center_x, bbox_center_y
                        confidence *= 1.1  # Increase confidence for bbox-derived coordinates

                # Final validation
                if 0 <= x < image_width and 0 <= y < image_height:
                    coordinates.append(ClickCoordinate(
                        x=int(x),
                        y=int(y),
                        confidence=min(confidence, 1.0),
                        description=description,
                        element_type=element_type
                    ))

                    status = "✅ VALID" if coordinate_valid else "⚠️  ADJUSTED"
                    logger.info(f"  {status} Final coordinates: ({x}, {y}) [Confidence: {confidence:.2%}]")
                else:
                    logger.error(f"  ❌ REJECTED: Final coordinates ({x}, {y}) still invalid")

            # Create analysis with enhanced metadata
            analysis = ChallengeAnalysis(
                challenge_type=data.get('challenge_type', 'unknown'),
                instructions=data.get('instructions', 'No instructions provided'),
                click_coordinates=coordinates,
                success_probability=data.get('success_probability', 0.5),
                next_steps=data.get('next_steps', ['Manual intervention required'])
            )

            logger.info(f"=== ANALYSIS SUMMARY ===")
            logger.info(f"Challenge: {analysis.challenge_type}")
            logger.info(f"Valid coordinates: {len(coordinates)}")
            logger.info(f"Success probability: {analysis.success_probability:.2%}")
            logger.info(f"=== END ANALYSIS ===")

            return analysis

        except Exception as e:
            logger.error(f"❌ Response parsing error: {e}")
            logger.error(f"Raw response text: {response_text[:500]}...")

            # Return safe fallback
            return ChallengeAnalysis(
                challenge_type="parsing_error",
                instructions="Could not parse AI response",
                click_coordinates=[],
                success_probability=0.0,
                next_steps=["Check logs", "Try manual intervention"]
            )

class AutomationController:
    """Controls mouse and keyboard automation with multi-monitor support"""

    def __init__(self, screen_capture: 'ScreenCapture' = None):
        # Configure PyAutoGUI
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        self.screen_capture = screen_capture

    def click_coordinate(self, coord: ClickCoordinate) -> bool:
        """Click at specified coordinate with multi-monitor coordinate translation"""
        try:
            # Get screen and monitor info for detailed logging
            screen_width, screen_height = pyautogui.size()
            monitor_info = self.screen_capture.monitor_offset if self.screen_capture else {'x': 0, 'y': 0}

            logger.info(f"=== COORDINATE CLICK DEBUG ===")
            logger.info(f"Screenshot coordinates: ({coord.x}, {coord.y})")
            logger.info(f"Monitor offset: {monitor_info}")
            logger.info(f"Screen size: {screen_width}x{screen_height}")
            logger.info(f"Element: {coord.description} (confidence: {coord.confidence:.2%})")

            # Translate coordinates for multi-monitor setup
            actual_x, actual_y = self._translate_coordinates(coord.x, coord.y)

            logger.info(f"Translated coordinates: ({actual_x}, {actual_y})")

            # Validate coordinates are within screen bounds
            if not self._validate_coordinates(actual_x, actual_y):
                logger.error(f"Coordinates ({actual_x}, {actual_y}) are outside screen bounds!")
                logger.error(f"Screen bounds: 0-{screen_width-1}, 0-{screen_height-1}")
                return False

            # Get current mouse position for comparison
            current_x, current_y = pyautogui.position()
            logger.info(f"Current mouse position: ({current_x}, {current_y})")

            # Move to coordinate smoothly
            logger.info(f"Moving mouse to ({actual_x}, {actual_y})...")
            pyautogui.moveTo(actual_x, actual_y, duration=0.5)
            time.sleep(0.2)

            # Verify mouse position
            verify_x, verify_y = pyautogui.position()
            logger.info(f"Mouse position after move: ({verify_x}, {verify_y})")

            if abs(verify_x - actual_x) > 5 or abs(verify_y - actual_y) > 5:
                logger.warning(f"Mouse position mismatch! Expected ({actual_x}, {actual_y}), got ({verify_x}, {verify_y})")

            # Click
            logger.info(f"Clicking at ({actual_x}, {actual_y})...")
            pyautogui.click(actual_x, actual_y)
            time.sleep(0.5)

            logger.info(f"Click completed successfully")
            logger.info(f"=== END COORDINATE DEBUG ===")

            return True

        except Exception as e:
            logger.error(f"Click error: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    def _translate_coordinates(self, screenshot_x: int, screenshot_y: int) -> tuple:
        """Ultra-precise coordinate translation with calibration support"""
        if not self.screen_capture:
            return screenshot_x, screenshot_y

        # Get monitor information
        monitor_offset = self.screen_capture.monitor_offset
        monitor_info = self.screen_capture.monitors[self.screen_capture.active_monitor]

        # Basic translation
        basic_x = screenshot_x + monitor_offset['x']
        basic_y = screenshot_y + monitor_offset['y']

        # Apply calibration correction if available
        calibrated_x, calibrated_y = self._apply_calibration_correction(
            screenshot_x, screenshot_y, basic_x, basic_y, monitor_info
        )

        # Validate final coordinates
        screen_width, screen_height = pyautogui.size()
        final_x = max(0, min(calibrated_x, screen_width - 1))
        final_y = max(0, min(calibrated_y, screen_height - 1))

        # Log translation details
        logger.info(f"Coordinate translation:")
        logger.info(f"  Screenshot: ({screenshot_x}, {screenshot_y})")
        logger.info(f"  Monitor offset: ({monitor_offset['x']}, {monitor_offset['y']})")
        logger.info(f"  Basic translation: ({basic_x}, {basic_y})")
        logger.info(f"  Calibrated: ({calibrated_x}, {calibrated_y})")
        logger.info(f"  Final: ({final_x}, {final_y})")

        return final_x, final_y

    def _apply_calibration_correction(self, screenshot_x, screenshot_y, basic_x, basic_y, monitor_info):
        """Apply calibration-based coordinate correction"""
        try:
            # Try to load calibration data
            calibration_file = f"calibration_data_{self.screen_capture.active_monitor}_latest.json"
            if os.path.exists(calibration_file):
                with open(calibration_file, 'r') as f:
                    calibration_data = json.load(f)

                # Apply calibration correction based on historical accuracy
                correction = self._calculate_calibration_correction(
                    screenshot_x, screenshot_y, calibration_data
                )

                corrected_x = basic_x + correction['x']
                corrected_y = basic_y + correction['y']

                logger.info(f"Applied calibration correction: ({correction['x']:.1f}, {correction['y']:.1f})")
                return corrected_x, corrected_y

        except Exception as e:
            logger.warning(f"Could not apply calibration correction: {e}")

        # Return basic translation if calibration fails
        return basic_x, basic_y

    def _calculate_calibration_correction(self, screenshot_x, screenshot_y, calibration_data):
        """Calculate coordinate correction based on calibration data"""
        # Simple correction based on average error from calibration
        test_results = calibration_data.get('test_results', [])

        if not test_results:
            return {'x': 0, 'y': 0}

        # Calculate average error offset
        total_error_x = sum(result['error_x'] for result in test_results)
        total_error_y = sum(result['error_y'] for result in test_results)

        avg_error_x = total_error_x / len(test_results)
        avg_error_y = total_error_y / len(test_results)

        # Apply correction (negative of average error)
        return {
            'x': -avg_error_x,
            'y': -avg_error_y
        }

    def _validate_coordinates(self, x: int, y: int) -> bool:
        """Validate that coordinates are within screen bounds"""
        try:
            screen_width, screen_height = pyautogui.size()
            return 0 <= x < screen_width and 0 <= y < screen_height
        except Exception as e:
            logger.error(f"Coordinate validation error: {e}")
            return True  # Allow click if validation fails
    
    def wait_for_element_change(self, region: Dict, timeout: int = 10) -> bool:
        """Wait for visual changes in a region"""
        try:
            initial_screenshot = pyautogui.screenshot(region=tuple(region.values()))
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                current_screenshot = pyautogui.screenshot(region=tuple(region.values()))
                if not self._images_similar(initial_screenshot, current_screenshot):
                    return True
                time.sleep(0.5)
            
            return False
            
        except Exception as e:
            logger.error(f"Wait for change error: {e}")
            return False
    
    def _images_similar(self, img1: Image.Image, img2: Image.Image, threshold: float = 0.95) -> bool:
        """Compare two images for similarity"""
        try:
            # Convert to numpy arrays
            arr1 = np.array(img1)
            arr2 = np.array(img2)
            
            # Calculate similarity
            diff = np.abs(arr1 - arr2)
            similarity = 1 - (np.sum(diff) / (arr1.size * 255))
            
            return similarity > threshold
            
        except Exception as e:
            logger.error(f"Image comparison error: {e}")
            return False

class CaptchaSolver:
    """Main solver class that coordinates all components"""

    def __init__(self, gemini_api_key: str):
        self.tts = TextToSpeechManager()
        self.screen_capture = ScreenCapture()
        self.gemini = GeminiAnalyzer(gemini_api_key)
        self.automation = AutomationController(self.screen_capture)  # Pass screen_capture for coordinate translation

        # Configuration
        self.max_attempts = 3
        self.wait_timeout = 30
        self.confidence_threshold = 0.7

        logger.info("CaptchaSolver initialized")
        logger.info(f"Active monitor: {self.screen_capture.active_monitor}")
        logger.info(f"Monitor offset: {self.screen_capture.monitor_offset}")
        self.tts.speak("Captcha solver ready for multi-monitor setup", wait=True)

    def solve_challenge(self, url: Optional[str] = None) -> bool:
        """
        Main method to solve challenges

        Step-by-step procedure:
        1. Capture current screen
        2. Analyze with Gemini AI
        3. Execute clicking sequence
        4. Verify success
        5. Provide feedback
        """
        try:
            self.tts.speak("Starting challenge analysis")

            for attempt in range(self.max_attempts):
                logger.info(f"Attempt {attempt + 1}/{self.max_attempts}")
                self.tts.speak(f"Attempt {attempt + 1}")

                # Step 1: Capture screen
                screenshot = self._capture_and_save_screenshot()

                # Step 2: Analyze with Gemini
                analysis = self.gemini.analyze_challenge(screenshot)

                # Step 3: Process analysis
                if analysis.challenge_type == "none":
                    self.tts.speak("No challenge detected")
                    return True

                # Step 4: Execute solution
                success = self._execute_solution(analysis)

                if success:
                    self.tts.speak("Challenge solved successfully")
                    return True

                # Wait before retry
                time.sleep(2)

            self.tts.speak("Failed to solve challenge after maximum attempts")
            return False

        except Exception as e:
            logger.error(f"Solve challenge error: {e}")
            self.tts.speak("Error occurred during challenge solving")
            return False

    def _capture_and_save_screenshot(self) -> Image.Image:
        """Capture and save screenshot for analysis"""
        screenshot = self.screen_capture.capture_screen()
        timestamp = int(time.time())
        filename = f"challenge_{timestamp}.png"
        self.screen_capture.save_screenshot(screenshot, filename)
        return screenshot

    def _execute_solution(self, analysis: ChallengeAnalysis) -> bool:
        """Execute the solution based on analysis"""
        try:
            logger.info(f"Executing solution for {analysis.challenge_type}")
            self.tts.speak(f"Solving {analysis.challenge_type} challenge")

            # Provide instructions to user
            self.tts.speak(analysis.instructions)

            # Execute clicks in sequence
            for coord in analysis.click_coordinates:
                if coord.confidence < self.confidence_threshold:
                    logger.warning(f"Low confidence click: {coord.confidence}")
                    continue

                self.tts.speak(f"Clicking {coord.description}")
                success = self.automation.click_coordinate(coord)

                if not success:
                    return False

                # Wait for response
                time.sleep(1)

            # Wait for challenge completion
            return self._wait_for_completion(analysis)

        except Exception as e:
            logger.error(f"Solution execution error: {e}")
            return False

    def _wait_for_completion(self, analysis: ChallengeAnalysis) -> bool:
        """Wait for challenge completion and verify success"""
        try:
            self.tts.speak("Waiting for verification")

            # Wait for page changes
            time.sleep(3)

            # Take new screenshot to verify
            new_screenshot = self.screen_capture.capture_screen()
            new_analysis = self.gemini.analyze_challenge(new_screenshot)

            # Check if challenge is resolved
            if new_analysis.challenge_type == "none":
                return True

            # If same challenge type, might need more interaction
            if new_analysis.challenge_type == analysis.challenge_type:
                logger.info("Challenge still present, may need additional steps")
                return False

            return True

        except Exception as e:
            logger.error(f"Completion verification error: {e}")
            return False

    def solve_cloudflare(self) -> bool:
        """Specialized method for Cloudflare challenges"""
        self.tts.speak("Detecting Cloudflare challenge")
        return self.solve_challenge()

    def solve_recaptcha(self) -> bool:
        """Specialized method for reCAPTCHA challenges"""
        self.tts.speak("Detecting reCAPTCHA challenge")
        return self.solve_challenge()

    def continuous_monitoring(self, interval: int = 5):
        """Continuously monitor for challenges"""
        self.tts.speak("Starting continuous monitoring mode")

        try:
            while True:
                screenshot = self.screen_capture.capture_screen()
                analysis = self.gemini.analyze_challenge(screenshot)

                if analysis.challenge_type != "none":
                    logger.info(f"Challenge detected: {analysis.challenge_type}")
                    self.solve_challenge()

                time.sleep(interval)

        except KeyboardInterrupt:
            self.tts.speak("Monitoring stopped")
            logger.info("Continuous monitoring stopped")

    def switch_monitor(self, monitor_id: int):
        """Switch to a different monitor for capture and clicking"""
        if monitor_id < len(self.screen_capture.monitors):
            self.screen_capture.active_monitor = monitor_id
            self.screen_capture.monitor_offset = self.screen_capture._get_monitor_offset()
            logger.info(f"Switched to monitor {monitor_id}")
            logger.info(f"New monitor offset: {self.screen_capture.monitor_offset}")
            self.tts.speak(f"Switched to monitor {monitor_id}")
        else:
            logger.error(f"Monitor {monitor_id} not found")
            self.tts.speak(f"Monitor {monitor_id} not available")

    def list_monitors(self):
        """List all available monitors"""
        monitors_info = []
        for i, monitor in enumerate(self.screen_capture.monitors[1:], 1):
            info = f"Monitor {i}: {monitor['width']}x{monitor['height']} at ({monitor['left']}, {monitor['top']})"
            monitors_info.append(info)
            logger.info(info)

        self.tts.speak(f"Found {len(monitors_info)} monitors")
        return monitors_info

    def test_coordinate_translation(self, screenshot_x: int, screenshot_y: int):
        """Test coordinate translation for debugging"""
        actual_x, actual_y = self.automation._translate_coordinates(screenshot_x, screenshot_y)
        logger.info(f"Screenshot coords ({screenshot_x}, {screenshot_y}) -> Actual coords ({actual_x}, {actual_y})")
        self.tts.speak(f"Screenshot coordinates {screenshot_x}, {screenshot_y} translate to actual coordinates {actual_x}, {actual_y}")
        return actual_x, actual_y

    def debug_mouse_position(self):
        """Debug current mouse position and monitor detection"""
        import pyautogui
        mouse_x, mouse_y = pyautogui.position()
        logger.info(f"Current mouse position: ({mouse_x}, {mouse_y})")

        # Check which monitor the mouse is on
        for i, monitor in enumerate(self.screen_capture.monitors[1:], 1):
            if (monitor['left'] <= mouse_x < monitor['left'] + monitor['width'] and
                monitor['top'] <= mouse_y < monitor['top'] + monitor['height']):
                logger.info(f"Mouse is on monitor {i}")
                self.tts.speak(f"Mouse is currently on monitor {i}")
                return i

        self.tts.speak("Mouse position could not be determined")
        return None

def main():
    """
    Main function with step-by-step procedure

    Usage Examples:
    1. Basic challenge solving
    2. Continuous monitoring
    3. Specific challenge types
    """

    # Step 1: Load configuration
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Error: GEMINI_API_KEY not found in environment variables")
        print("Please set your Gemini API key in .env file")
        return

    # Step 2: Initialize solver
    try:
        solver = CaptchaSolver(api_key)
    except Exception as e:
        print(f"Failed to initialize solver: {e}")
        return

    # Step 3: Display monitor information
    print(f"\nDetected Monitors:")
    monitors_info = solver.list_monitors()
    for info in monitors_info:
        print(f"  {info}")
    print(f"Active monitor: {solver.screen_capture.active_monitor}")
    print(f"Monitor offset: {solver.screen_capture.monitor_offset}")

    # Step 4: Choose operation mode
    print("\nCaptcha Solver - Choose operation mode:")
    print("1. Solve current challenge")
    print("2. Continuous monitoring")
    print("3. Solve Cloudflare specifically")
    print("4. Solve reCAPTCHA specifically")
    print("5. Switch monitor")
    print("6. Debug mouse position")
    print("7. Test coordinate translation")

    try:
        choice = input("Enter choice (1-7): ").strip()

        if choice == "1":
            success = solver.solve_challenge()
            print(f"Challenge solving {'successful' if success else 'failed'}")

        elif choice == "2":
            print("Starting continuous monitoring (Press Ctrl+C to stop)")
            solver.continuous_monitoring()

        elif choice == "3":
            success = solver.solve_cloudflare()
            print(f"Cloudflare challenge {'solved' if success else 'failed'}")

        elif choice == "4":
            success = solver.solve_recaptcha()
            print(f"reCAPTCHA challenge {'solved' if success else 'failed'}")

        elif choice == "5":
            monitor_id = int(input("Enter monitor number: "))
            solver.switch_monitor(monitor_id)

        elif choice == "6":
            solver.debug_mouse_position()

        elif choice == "7":
            x = int(input("Enter screenshot X coordinate: "))
            y = int(input("Enter screenshot Y coordinate: "))
            actual_x, actual_y = solver.test_coordinate_translation(x, y)
            print(f"Translated coordinates: ({actual_x}, {actual_y})")

        else:
            print("Invalid choice")

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        print(f"Error during operation: {e}")

if __name__ == "__main__":
    main()
