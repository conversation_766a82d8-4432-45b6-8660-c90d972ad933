# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyDuVG5s3KlyQfs-TwVom6ag_b7itS3FYxI

# Audio Settings (optional)
SPEECH_RATE=150  # Words per minute (100-200 recommended)
VOLUME_LEVEL=0.9  # 0.0 to 1.0

# Screen Resolution (optional)
SCREEN_WIDTH=  # Auto-detected if not specified
SCREEN_HEIGHT=  # Auto-detected if not specified

# Safety Settings
CLICK_DELAY=1.0  # Seconds between automated clicks (default: 1.0)
FAILSAFE=True  # Enable/disable pyautogui failsafe (True/False)

# Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
SAVE_SCREENSHOTS=True  # Save processed CAPTCHA images for debugging

# Cloudflare Settings (optional)
CF_MAX_RETRIES=3  # Number of attempts for Cloudflare challenges
CF_TIMEOUT=30  # Seconds to wait for Cloudflare verification