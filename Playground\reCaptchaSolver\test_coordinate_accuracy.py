#!/usr/bin/env python3
"""
Comprehensive Coordinate Accuracy Test
Tests and validates coordinate mapping accuracy for the captcha solver
"""

import os
import sys
import time
import json
import math
import pyautogui
import mss
from PIL import Image, ImageDraw, ImageFont

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_coordinate_accuracy():
    """Comprehensive coordinate accuracy test"""
    print("🎯 COMPREHENSIVE COORDINATE ACCURACY TEST")
    print("=" * 60)
    
    # Initialize components
    sct = mss.mss()
    monitors = sct.monitors
    
    print(f"Detected {len(monitors)-1} monitors")
    
    # Detect active monitor
    mouse_x, mouse_y = pyautogui.position()
    active_monitor = 1
    
    for i, monitor in enumerate(monitors[1:], 1):
        print(f"Monitor {i}: {monitor['width']}x{monitor['height']} at ({monitor['left']}, {monitor['top']})")
        if (monitor['left'] <= mouse_x < monitor['left'] + monitor['width'] and
            monitor['top'] <= mouse_y < monitor['top'] + monitor['height']):
            active_monitor = i
            print(f"  ✅ Active monitor (mouse detected)")
    
    monitor_info = monitors[active_monitor]
    print(f"\nUsing monitor {active_monitor} for testing")
    
    # Test 1: Basic coordinate translation
    print(f"\n📐 TEST 1: Basic Coordinate Translation")
    print("-" * 40)
    
    test_points = [
        (100, 100, "Top-Left"),
        (monitor_info['width']//2, monitor_info['height']//2, "Center"),
        (monitor_info['width']-100, monitor_info['height']-100, "Bottom-Right"),
        (200, 300, "Custom Point 1"),
        (600, 400, "Custom Point 2")
    ]
    
    translation_results = []
    
    for screenshot_x, screenshot_y, description in test_points:
        # Calculate expected actual coordinates
        expected_x = screenshot_x + monitor_info['left']
        expected_y = screenshot_y + monitor_info['top']
        
        print(f"\n{description}:")
        print(f"  Screenshot coords: ({screenshot_x}, {screenshot_y})")
        print(f"  Expected actual: ({expected_x}, {expected_y})")
        
        # Move mouse and test
        pyautogui.moveTo(expected_x, expected_y, duration=0.3)
        time.sleep(0.1)
        
        actual_x, actual_y = pyautogui.position()
        error_x = abs(actual_x - expected_x)
        error_y = abs(actual_y - expected_y)
        total_error = math.sqrt(error_x**2 + error_y**2)
        
        print(f"  Actual position: ({actual_x}, {actual_y})")
        print(f"  Error: {total_error:.1f}px")
        
        result = {
            'description': description,
            'screenshot_coords': (screenshot_x, screenshot_y),
            'expected_coords': (expected_x, expected_y),
            'actual_coords': (actual_x, actual_y),
            'error': total_error,
            'accuracy': max(0, 100 - total_error)
        }
        translation_results.append(result)
    
    # Test 2: Precision grid test
    print(f"\n🔍 TEST 2: Precision Grid Test")
    print("-" * 40)
    
    # Create precision test image
    screenshot = sct.grab(monitor_info)
    img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
    
    # Add precision markers
    precision_img = add_precision_markers(img)
    timestamp = int(time.time())
    precision_file = f"precision_test_{timestamp}.png"
    precision_img.save(precision_file)
    print(f"Precision test image saved: {precision_file}")
    
    # Test 3: Edge case coordinates
    print(f"\n⚠️  TEST 3: Edge Case Coordinates")
    print("-" * 40)
    
    edge_cases = [
        (0, 0, "Origin"),
        (1, 1, "Near Origin"),
        (monitor_info['width']-1, monitor_info['height']-1, "Max Bounds"),
        (monitor_info['width']-2, monitor_info['height']-2, "Near Max"),
    ]
    
    edge_results = []
    
    for screenshot_x, screenshot_y, description in edge_cases:
        expected_x = screenshot_x + monitor_info['left']
        expected_y = screenshot_y + monitor_info['top']
        
        # Validate bounds
        screen_width, screen_height = pyautogui.size()
        if not (0 <= expected_x < screen_width and 0 <= expected_y < screen_height):
            print(f"{description}: ❌ Out of screen bounds")
            continue
        
        pyautogui.moveTo(expected_x, expected_y, duration=0.2)
        time.sleep(0.1)
        
        actual_x, actual_y = pyautogui.position()
        error = math.sqrt((actual_x - expected_x)**2 + (actual_y - expected_y)**2)
        
        print(f"{description}: Error {error:.1f}px")
        edge_results.append({
            'description': description,
            'error': error,
            'passed': error < 5
        })
    
    # Test 4: Rapid succession test
    print(f"\n⚡ TEST 4: Rapid Succession Test")
    print("-" * 40)
    
    rapid_points = [
        (200, 200), (400, 300), (600, 400), (300, 500), (500, 200)
    ]
    
    rapid_results = []
    start_time = time.time()
    
    for i, (screenshot_x, screenshot_y) in enumerate(rapid_points):
        expected_x = screenshot_x + monitor_info['left']
        expected_y = screenshot_y + monitor_info['top']
        
        pyautogui.moveTo(expected_x, expected_y, duration=0.1)
        actual_x, actual_y = pyautogui.position()
        
        error = math.sqrt((actual_x - expected_x)**2 + (actual_y - expected_y)**2)
        rapid_results.append(error)
        
        print(f"Point {i+1}: {error:.1f}px error")
    
    total_time = time.time() - start_time
    print(f"Total time: {total_time:.2f}s")
    
    # Generate comprehensive report
    print(f"\n📊 COMPREHENSIVE ACCURACY REPORT")
    print("=" * 60)
    
    # Basic translation stats
    basic_errors = [r['error'] for r in translation_results]
    basic_avg = sum(basic_errors) / len(basic_errors)
    basic_max = max(basic_errors)
    basic_min = min(basic_errors)
    
    print(f"BASIC TRANSLATION TEST:")
    print(f"  Average error: {basic_avg:.1f}px")
    print(f"  Maximum error: {basic_max:.1f}px")
    print(f"  Minimum error: {basic_min:.1f}px")
    print(f"  Tests under 5px: {len([e for e in basic_errors if e < 5])}/{len(basic_errors)}")
    
    # Edge case stats
    edge_passed = len([r for r in edge_results if r['passed']])
    print(f"\nEDGE CASE TEST:")
    print(f"  Passed: {edge_passed}/{len(edge_results)}")
    
    # Rapid succession stats
    rapid_avg = sum(rapid_results) / len(rapid_results)
    print(f"\nRAPID SUCCESSION TEST:")
    print(f"  Average error: {rapid_avg:.1f}px")
    print(f"  Speed: {len(rapid_points)/total_time:.1f} points/second")
    
    # Overall assessment
    overall_score = calculate_overall_score(basic_avg, edge_passed, len(edge_results), rapid_avg)
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    print(f"  Accuracy Score: {overall_score:.1f}/100")
    
    if overall_score >= 90:
        print(f"  Status: ✅ EXCELLENT - Ready for production")
        recommendation = "System is highly accurate and ready for use."
    elif overall_score >= 75:
        print(f"  Status: ⚠️  GOOD - Minor adjustments recommended")
        recommendation = "System is functional but could benefit from calibration."
    elif overall_score >= 60:
        print(f"  Status: ⚠️  FAIR - Calibration recommended")
        recommendation = "System needs calibration for optimal performance."
    else:
        print(f"  Status: ❌ POOR - Recalibration required")
        recommendation = "System requires immediate recalibration."
    
    print(f"  Recommendation: {recommendation}")
    
    # Save detailed results
    detailed_results = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'monitor_info': monitor_info,
        'basic_translation': translation_results,
        'edge_cases': edge_results,
        'rapid_succession': rapid_results,
        'statistics': {
            'basic_avg_error': basic_avg,
            'basic_max_error': basic_max,
            'basic_min_error': basic_min,
            'edge_pass_rate': edge_passed / len(edge_results) if edge_results else 0,
            'rapid_avg_error': rapid_avg,
            'overall_score': overall_score
        },
        'recommendation': recommendation
    }
    
    results_file = f"coordinate_accuracy_results_{timestamp}.json"
    with open(results_file, 'w') as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"\n📄 Detailed results saved: {results_file}")
    
    return overall_score >= 75

def add_precision_markers(img):
    """Add precision markers to test image"""
    marked_img = img.copy()
    draw = ImageDraw.Draw(marked_img)
    width, height = img.size
    
    try:
        font = ImageFont.truetype("arial.ttf", 12)
    except:
        font = ImageFont.load_default()
    
    # Add grid
    grid_size = 50
    for x in range(0, width, grid_size):
        draw.line([(x, 0), (x, height)], fill="lightgray", width=1)
    for y in range(0, height, grid_size):
        draw.line([(0, y), (width, y)], fill="lightgray", width=1)
    
    # Add precision targets
    targets = [
        (width//4, height//4, "Q1"),
        (3*width//4, height//4, "Q2"),
        (width//4, 3*height//4, "Q3"),
        (3*width//4, 3*height//4, "Q4"),
        (width//2, height//2, "CENTER")
    ]
    
    for x, y, label in targets:
        # Draw target
        for radius in [5, 10, 15]:
            draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                        outline="red", width=2)
        
        # Draw crosshair
        draw.line([(x-20, y), (x+20, y)], fill="red", width=2)
        draw.line([(x, y-20), (x, y+20)], fill="red", width=2)
        
        # Add label
        draw.text((x+25, y-10), f"{label}\n({x},{y})", fill="red", font=font)
    
    return marked_img

def calculate_overall_score(basic_avg, edge_passed, edge_total, rapid_avg):
    """Calculate overall accuracy score"""
    # Basic accuracy score (0-50 points)
    basic_score = max(0, 50 - basic_avg * 5)
    
    # Edge case score (0-25 points)
    edge_score = (edge_passed / edge_total * 25) if edge_total > 0 else 0
    
    # Rapid succession score (0-25 points)
    rapid_score = max(0, 25 - rapid_avg * 2.5)
    
    return basic_score + edge_score + rapid_score

def main():
    """Main test function"""
    print("Starting comprehensive coordinate accuracy test...")
    print("This will move your mouse cursor to test coordinate precision.")
    print("Watch the mouse movement to verify accuracy.")
    
    input("Press Enter to start the test...")
    
    try:
        success = test_coordinate_accuracy()
        
        print(f"\n🏁 TEST COMPLETED")
        if success:
            print("✅ Coordinate system is accurate enough for production use.")
        else:
            print("⚠️  Coordinate system needs improvement.")
            print("Consider running the calibration tool: python coordinate_calibration.py")
        
    except KeyboardInterrupt:
        print(f"\n\nTest interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
