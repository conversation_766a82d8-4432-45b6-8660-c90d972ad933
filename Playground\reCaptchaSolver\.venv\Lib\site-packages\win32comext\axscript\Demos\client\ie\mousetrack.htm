<HTML>

<HEAD><TITLE>Python Scripting sample:  Mouse tracking</TITLE></HEAD>
<BODY BGCOLOR="#FFFFFF" TOPMARGIN=8>
<FONT SIZE=5>
<TABLE Border=0><TR VALIGN=MIDDLE><TD>
<A ID="Image"> <IMG
SRC="file:..\..\..\..\..\win32com\html\image\pycom_blowing.gif"
ALT="Clickable Map Image" HEIGHT=113 WIDTH=624 BORDER=0></A>

</TD></TR>
 <TR><TD>&nbsp;</TD></TR>
 <TR VALIGN=MIDDLE><TD VALIGN=MIDDLE ALIGN=CENTER><FONT SIZE=5><INPUT
TYPE="text" NAME="TxtLinkDescription" SIZE=50></FONT></TD></TR></TABLE>
</FONT>

<P>
A mouse tracking demo.  Move the mouse over the image above...

<SCRIPT Language="Python">
<!--
# Remember the last location clicked
# print("here we go", 1)
mx = my = 0

# class for rectangle testing
class rect:
   def __init__(self, lowx, lowy, upx, upy, desc, url):
       self.lowx, self.lowy, self.upx, self.upy, self.desc, self.url = \
         lowx, lowy, upx, upy, desc, url
   def inside(self, x, y):
       # print((x,y), "inside", self.desc)
       result = self.lowx <= x <= self.upx and self.lowy <= y <= self.upy
       # print(result)
       return result
   def mouse_move(self):
       # print("move", self.desc)
       ax.TxtLinkDescription.Value = coords + " - " + self.desc
   def onclick(self):
       # print("click", self.desc)
       ax.TxtLinkDescription.Value = coords +" click! " + `self.url`
       if self.url: ax.location = self.url

blows = "Blows away "
rects =[rect(12,48,59,101,blows+"Visual Basic", ""),
	rect(107,0,172,58,blows+"Internet Explorer", ""),
	rect(193,0,261,56,blows+"Microsoft Access", ""),
	rect(332,43,392,93,blows+"Microsoft Word", ""),
	rect(457,52,521,99,blows+"Microsoft Excel", ""),
	rect(537,12,613,85,"Python blows them all away!", "https://www.python.org"),
]

default = rect(0,0,0,0,"Click on an icon","")

def Image_MouseMove(s, b, x, y):
    global mx, my, coords
    coords =`(x,y)`
    # print(coords)
    mx, my = x, y
    for r in rects:
        if r.inside(x,y):
           # print(r.desc)
           r.mouse_move()
           break
    else:
        # print(default.desc)
        default.mouse_move()

def Image_OnClick():
    for r in rects:
        if r.inside(mx,my):
           r.onclick()
           break
-->
</SCRIPT>

<P>

</FONT>

</BODY>
</HTML>
