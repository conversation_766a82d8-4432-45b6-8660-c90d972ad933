from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    SPVPRI_NORMAL, DISPID_SPEsCount, DISPID_SVEventInterests,
    SPEI_MIN_SR, eLEXTYPE_PRIVATE13, DISPID_SDKEnumKeys, SLTApp,
    ISpEventSink, SpMMAudioOut, DISPID_SOTGetDescription,
    STSF_LocalAppData, DISPID_SRCERequestUI, BSTR, SPPS_RESERVED1,
    SpeechAudioVolume, SPPH<PERSON>SERULE, DISPID_SOTRemove, SRESoundEnd,
    SpObjectToken, SRSEIsSpeaking, IServiceProvider, SPVPRI_ALERT,
    DISPID_SPIProperties, DISPID_SREmulateRecognition,
    ISpSerializeState, SAFT32kHz16BitMono, DISPID_SRGState,
    DISPID_SVVolume, eLEXTYPE_PRIVATE19, SVP_3,
    DISPID_SLWPronunciations, SAFTGSM610_8kHzMono,
    ISpeechGrammarRuleState, SpPhoneConverter, SP_VISEME_7,
    ISpeechRecoGrammar, SPWORDLIST, eLEXTYPE_PRIVATE14,
    DISPID_SPEAudioSizeBytes, SSFMCreate, DISPID_SAFSetWaveFormatEx,
    VARIANT_BOOL, SpPhraseInfoBuilder, DISPID_SRSAudioStatus,
    DISPID_SWFEBlockAlign, wireHWND, SPRECORESULTTIMES,
    DISPID_SGRsCount, SpTextSelectionInformation, SVP_1, SINoSignal,
    tagSTATSTG, DISPID_SRCBookmark, DISPID_SVEStreamStart,
    DISPID_SDKDeleteValue, DISPID_SBSWrite, DISPID_SVPause, ISpAudio,
    eLEXTYPE_USER, SPWF_INPUT, SSTTWildcard, DISPID_SLWWord,
    SpObjectTokenCategory, DISPID_SGRSTPropertyId, SDTPronunciation,
    DISPID_SRAudioInputStream, SDA_Two_Trailing_Spaces,
    DISPID_SPEsItem, DISPID_SPIEnginePrivateData, SPFM_CREATE_ALWAYS,
    STCRemoteServer, DISPID_SLPsCount, SpWaveFormatEx, SPPS_RESERVED2,
    DISPID_SVVoice, ISpeechDataKey, SVP_6, SVESentenceBoundary,
    IUnknown, DISPID_SRCERecognition, SRTAutopause,
    DISPID_SRCEPropertyStringChange, WAVEFORMATEX,
    SAFTCCITT_uLaw_11kHzStereo, ISpeechResourceLoader,
    DISPID_SPEAudioSizeTime, DISPID_SRCRequestedUIType,
    SAFTTrueSpeech_8kHz1BitMono, SAFTADPCM_22kHzStereo,
    DISPID_SRCESoundStart, ISpeechGrammarRuleStateTransitions,
    DISPID_SRRTimes, DISPID_SRCEEnginePrivate, DISPID_SRSClsidEngine,
    DISPID_SPIGrammarId, DISPID_SASState, SAFT11kHz16BitStereo,
    DISPID_SRCEInterference, DISPID_SRCESoundEnd, DISPID_SVStatus,
    DISPID_SRRAlternates, SPSMF_UPS, SPEI_PHONEME, eLEXTYPE_PRIVATE11,
    SPSHT_EMAIL, DISPID_SRSCurrentStreamNumber, DISPID_SDKDeleteKey,
    SRAImport, DISPID_SRCCmdMaxAlternates, _ISpeechRecoContextEvents,
    eLEXTYPE_PRIVATE15, SPINTERFERENCE_NONE, STCAll, ISpStream,
    SVSFVoiceMask, DISPID_SBSSeek, DISPID_SPCIdToPhone,
    DISPID_SGRsAdd, ISpNotifySink, SpLexicon,
    DISPIDSPTSI_ActiveLength, __MIDL_IWinTypes_0009, DISPID_SPCLangId,
    SAFTADPCM_11kHzMono, SPSNotOverriden, ISpeechObjectTokens,
    DISPID_SVSLastResult, SPEI_PROPERTY_STRING_CHANGE,
    DISPID_SOTRemoveStorageFileName, SREStateChange, SREInterference,
    SPSUnknown, SDTProperty, DISPID_SOTMatchesAttributes,
    SGSExclusive, SDKLLocalMachine, SP_VISEME_13, SVEPrivate,
    Speech_StreamPos_Asap, eWORDTYPE_DELETED,
    DISPID_SVSLastBookmarkId, DISPID_SPRuleFirstElement,
    DISPID_SASNonBlockingIO, SpeechAudioProperties, ISpRecoContext2,
    SAFTExtendedAudioFormat, SpMemoryStream,
    SpeechRegistryLocalMachineRoot, DISPID_SPERetainedStreamOffset,
    IStream, DISPID_SRRTOffsetFromStart, SREBookmark,
    DISPID_SRGDictationSetState, SPEI_RESERVED1,
    DISPID_SVSInputSentenceLength, SpeechRecoProfileProperties,
    ISpeechPhraseReplacements, SLTUser, DISPID_SVAlertBoundary,
    SPPHRASE, SREStreamEnd, DISPID_SDKSetBinaryValue,
    SAFTCCITT_ALaw_11kHzMono, DISPID_SPRuleName,
    SpeechEngineProperties, DISPID_SWFEAvgBytesPerSec,
    SVEEndInputStream, SpCustomStream, ISpMMSysAudio,
    ISpeechLexiconPronunciation, DISPID_SVSInputWordPosition,
    SPEI_ACTIVE_CATEGORY_CHANGED, DISPID_SRRDiscardResultInfo,
    DISPID_SRGCmdLoadFromFile, SVP_17, DISPID_SGRsCommit, SVP_0,
    SPGS_ENABLED, DISPID_SRGetFormat, DISPID_SRDisplayUI,
    SPEI_WORD_BOUNDARY, COMMETHOD, _LARGE_INTEGER, DISPID_SFSOpen,
    DISPID_SGRId, SVP_18, DISPID_SLWs_NewEnum, SPXRO_SML,
    DISPID_SPRText, SAFTDefault, DISPID_SPPFirstElement,
    DISPID_SPPs_NewEnum, ISpeechCustomStream, DISPID_SRCResume,
    SPRULE, SpeechCategoryAppLexicons, ISpResourceManager,
    SPSModifier, DISPID_SABufferInfo, SpStreamFormatConverter,
    SLOStatic, DISPID_SVRate, ISpeechPhraseInfo, DISPID_SRRecognizer,
    DISPID_SDKGetStringValue, SPCT_SUB_COMMAND,
    DISPID_SDKGetBinaryValue, STSF_CommonAppData,
    DISPID_SVSInputWordLength, DISPID_SLWsItem, SPEI_PHRASE_START,
    SPEI_END_SR_STREAM, DISPID_SOTSetId, SpeechRegistryUserRoot,
    DISPID_SWFEFormatTag, SAFTCCITT_uLaw_22kHzMono,
    DISPID_SPRulesCount, DISPID_SMSSetData, SDA_No_Trailing_Space,
    SVSFUnusedFlags, SAFT8kHz16BitStereo, DISPID_SWFEBitsPerSample,
    SPEI_SR_AUDIO_LEVEL, DISPID_SPEDisplayAttributes, SVF_Emphasis,
    DISPID_SFSClose, SPAR_Unknown, DISPID_SPERequiredConfidence,
    _ISpeechVoiceEvents, ISpeechPhraseAlternate, DISPID_SGRSTRule,
    eLEXTYPE_PRIVATE12, SREStreamStart, DISPID_SPIGetText,
    SpeechCategoryRecoProfiles, SPBO_AHEAD, SDTDisplayText,
    DISPID_SOTId, SAFTCCITT_uLaw_44kHzStereo, helpstring,
    SP_VISEME_14, SPEI_RECO_OTHER_CONTEXT, eLEXTYPE_PRIVATE18,
    SPDKL_CurrentConfig, DISPID_SVResume, DISPID_SLGetWords,
    DISPID_SRCEHypothesis, DISPID_SDKOpenKey, DISPID_SGRSTText,
    DISPID_SASCurrentDevicePosition, SECFIgnoreWidth, SGSDisabled,
    DISPID_SRIsUISupported, SGDSActiveUserDelimited,
    SpInProcRecoContext, SVSFNLPMask, SpeechVoiceCategoryTTSRate,
    DISPID_SLWsCount, eLEXTYPE_PRIVATE16, SAFT44kHz16BitMono,
    SAFTGSM610_11kHzMono, DISPID_SOTsItem, SPINTERFERENCE_NOSIGNAL,
    DISPID_SLPLangId, SASStop, DISPID_SGRSAddSpecialTransition,
    DISPID_SVEPhoneme, SPWT_LEXICAL, DISPID_SVGetAudioInputs,
    ISpeechPhraseReplacement, SPCS_DISABLED, SPEVENTSOURCEINFO,
    DISPID_SDKSetStringValue, DISPID_SGRSTransitions, DISPID_SAFType,
    DISPID_SPPConfidence, DISPID_SRGetPropertyNumber,
    SpPhoneticAlphabetConverter, SREAllEvents, SVP_10,
    ISpPhoneConverter, DISPID_SGRs_NewEnum, ISpeechRecoResultTimes,
    DISPID_SPRNumberOfElements, ISpeechRecoResultDispatch, SGRSTTWord,
    DISPID_SVSPhonemeId, SPFM_NUM_MODES, SpResourceManager,
    ISpeechXMLRecoResult, SGLexical, DISPID_SGRsCommitAndSave,
    DISPID_SRGCmdLoadFromMemory, DISPID_SRCAudioInInterferenceStatus,
    SpeechGrammarTagDictation, SPLO_STATIC, SPPS_Verb,
    eLEXTYPE_RESERVED7, eLEXTYPE_PRIVATE9, SITooLoud,
    SAFTCCITT_uLaw_11kHzMono, SPRS_ACTIVE, SVEStartInputStream,
    DISPID_SRRTLength, DISPID_SPIAudioSizeBytes, SPEI_RESERVED2,
    ISpStreamFormat, SPCT_DICTATION, DISPID_SOTGetStorageFileName,
    SAFT44kHz16BitStereo, SpUnCompressedLexicon, SPCT_SLEEP,
    DISPID_SGRInitialState, SAFTCCITT_ALaw_44kHzMono,
    DISPID_SPRuleConfidence, SpeechCategoryRecognizers,
    SRSActiveAlways, eLEXTYPE_PRIVATE1, DISPID_SPEAudioStreamOffset,
    SPPS_Noncontent, SpeechCategoryVoices, SAFTADPCM_11kHzStereo,
    SpeechPropertyLowConfidenceThreshold, DISPID_SRGId,
    DISPID_SVEViseme, DISPID_SPRuleChildren, SRTStandard,
    DISPID_SOTDisplayUI, DISPID_SRCEAudioLevel, SWTAdded,
    SAFT22kHz16BitStereo, DISPID_SPANumberOfElementsInResult,
    ISpeechAudioBufferInfo, SINoise, ISpeechLexicon,
    eLEXTYPE_USER_SHORTCUT, SpeechCategoryAudioOut,
    DISPID_SVIsUISupported, SVEAllEvents, DISPID_SRGetPropertyString,
    DISPID_SRGReset, DISPID_SRGSetTextSelection, dispid,
    ISpeechLexiconWord, ISpeechLexiconPronunciations,
    SPINTERFERENCE_TOOQUIET, SRSInactiveWithPurge,
    SPWORDPRONUNCIATIONLIST, SPSHT_OTHER, VARIANT, SPAR_High,
    SPAS_STOP, SPEI_SR_RETAINEDAUDIO, SPWF_SRENGINE,
    DISPID_SPPChildren, SPLO_DYNAMIC, SECFIgnoreKanaType,
    DISPID_SWFESamplesPerSec, DISPID_SGRAttributes,
    ISpNotifyTranslator, DISPID_SRRGetXMLResult,
    DISPID_SRGCmdLoadFromResource,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, SREFalseRecognition,
    SpMMAudioIn, DISPID_SLWLangId, SPPS_NotOverriden,
    DISPID_SLPSymbolic, DISPID_SPRuleId, ISpeechWaveFormatEx,
    IEnumString, SAFTNonStandardFormat, SPEI_MAX_SR,
    SGDSActiveWithAutoPause, SPCT_SUB_DICTATION, SAFT8kHz8BitMono,
    SpeechPropertyResponseSpeed, ISpeechGrammarRuleStateTransition,
    DISPID_SPRDisplayAttributes, DISPID_SLPPartOfSpeech,
    SRTExtendableParse, SPEI_TTS_PRIVATE,
    SPINTERFERENCE_LATENCY_WARNING, SPSVerb, SpeechAddRemoveWord,
    DISPID_SPEEngineConfidence, DISPID_SRRSaveToMemory,
    ISpeechLexiconWords, __MIDL___MIDL_itf_sapi_0000_0020_0001,
    SPEI_END_INPUT_STREAM, SVSFParseSsml, SECFNoSpecialChars,
    SPINTERFERENCE_TOOSLOW, DISPID_SPPNumberOfElements,
    ISpeechBaseStream, typelib_path, DISPID_SPRFirstElement, SVP_12,
    SVEViseme, SPEI_TTS_AUDIO_LEVEL, SPEI_PROPERTY_NUM_CHANGE,
    DISPID_SOTCId, DISPID_SRCERecognitionForOtherContext,
    eLEXTYPE_RESERVED4, DISPID_SGRsFindRule, DISPID_SOTCreateInstance,
    SPINTERFERENCE_LATENCY_TRUNCATE_END, ISpeechObjectTokenCategory,
    SpeechAllElements, DISPMETHOD, Speech_StreamPos_RealTime,
    SPSHT_NotOverriden, SDTReplacement, SVP_16,
    DISPID_SRGSetWordSequenceData, SPSERIALIZEDPHRASE,
    SpeechTokenKeyAttributes, DISPID_SLGetGenerationChange, UINT_PTR,
    SECHighConfidence, DISPID_SPISaveToMemory, DISPID_SDKEnumValues,
    ISpeechPhraseRule, DISPID_SLPPhoneIds, eLEXTYPE_PRIVATE2,
    SPPS_LMA, SREAdaptation, ISpeechFileStream, SVSFParseSapi,
    SPINTERFERENCE_NOISE, SVSFIsNotXML, SAFTADPCM_44kHzStereo,
    SPPS_Unknown, SPRST_ACTIVE_ALWAYS, DISPID_SGRSTsCount, SWTDeleted,
    SPEI_VISEME, ISpeechMemoryStream,
    DISPID_SVSLastStreamNumberQueued, SVP_11,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet,
    DISPID_SVSpeakStream, LONG_PTR, SVEBookmark, DISPID_SASetState,
    SECNormalConfidence, DISPID_SPEActualConfidence,
    DISPID_SPERetainedSizeBytes, DISPID_SPRs_NewEnum, ULONG_PTR,
    ISpPhoneticAlphabetSelection, SPEI_RESERVED6, SAFT16kHz8BitMono,
    SPEI_UNDEFINED, DISPID_SPRuleParent, SPINTERFERENCE_TOOFAST,
    SP_VISEME_8, SAFT12kHz8BitStereo, SPEI_START_INPUT_STREAM,
    WSTRING, DISPID_SBSRead, SpeechPropertyResourceUsage,
    DISPID_SGRSAddRuleTransition, eLEXTYPE_VENDORLEXICON, SDTAll,
    STSF_AppData, SPRS_INACTIVE, SpeechVoiceSkipTypeSentence,
    SRAExport, DISPID_SVSLastBookmark, SpStream, SVF_None,
    DISPID_SVSInputSentencePosition, DISPID_SPRuleEngineConfidence,
    DISPID_SPCPhoneToId, ISpeechPhraseRules, DISPID_SRState,
    DISPID_SGRName, SPGS_DISABLED, SREPhraseStart, SAFT16kHz16BitMono,
    DISPID_SGRClear, DISPID_SVAudioOutput, DISPID_SRProfile,
    DISPID_SVEBookmark, DISPID_SGRSRule, SVSFIsXML,
    SPDKL_DefaultLocation, SPCT_COMMAND, SVPNormal,
    SPDKL_LocalMachine, SPPHRASEELEMENT, SPBO_NONE,
    SpeechPropertyHighConfidenceThreshold,
    DISPID_SRCEFalseRecognition, SVSFIsFilename,
    DISPID_SVSyncronousSpeakTimeout, SECFEmulateResult,
    SPAO_RETAIN_AUDIO, DISPID_SVEStreamEnd, SPCS_ENABLED,
    SREAudioLevel, DISPID_SGRSAddWordTransition, SPWORDPRONUNCIATION,
    SPDKL_CurrentUser, SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE, SITooFast,
    Speech_Max_Word_Length, SPPS_RESERVED4, SGDisplay, _check_version,
    SVSFNLPSpeakPunc, SWPUnknownWordUnpronounceable,
    SPTEXTSELECTIONINFO, STCInprocServer, ISpShortcut,
    SAFT32kHz8BitMono, SpMMAudioEnum, DISPID_SASCurrentSeekPosition,
    SP_VISEME_17, DISPID_SVAudioOutputStream,
    DISPID_SLAddPronunciation, SREPrivate, SDTAudio,
    DISPID_SPEs_NewEnum, DISPID_SPIElements, DISPID_SPIEngineId,
    SPRST_ACTIVE, DISPID_SRRPhraseInfo, SFTSREngine, SPPROPERTYINFO,
    SPRST_NUM_STATES, DISPID_SOTCSetId, DISPID_SPACommit, SVP_5,
    SAFTGSM610_44kHzMono, DISPID_SRSetPropertyString,
    DISPID_SRCEBookmark, DISPID_SLAddPronunciationByPhoneIds,
    DISPID_SABIEventBias, SSFMOpenForRead, ISpRecognizer3, SRADynamic,
    eLEXTYPE_RESERVED6, DISPID_SRRGetXMLErrorInfo, SP_VISEME_21,
    SPXRO_Alternates_SML, SpeechTokenIdUserLexicon, SASRun,
    DISPID_SAEventHandle, SSSPTRelativeToCurrentPosition,
    SAFTNoAssignedFormat, SPFM_CREATE, eLEXTYPE_PRIVATE3,
    DISPID_SVSkip, SRESoundStart, SpeechTokenKeyUI, SVP_14,
    DISPID_SPRules_NewEnum, DISPID_SRGIsPronounceable, SDTRule,
    SDTLexicalForm, DISPID_SRGCmdSetRuleState, ISpProperties, SPWORD,
    SpeechMicTraining, SAFT11kHz16BitMono, eLEXTYPE_PRIVATE17,
    SPPHRASEREPLACEMENT, ISpeechTextSelectionInformation,
    DISPID_SMSGetData, DISPID_SRSCurrentStreamPosition,
    DISPID_SRGDictationUnload, ISpeechObjectToken, HRESULT,
    SGRSTTRule, SRAInterpreter, ISpeechMMSysAudio, DISPID_SRCVoice,
    SRAONone, SITooSlow, _ULARGE_INTEGER, DISPID_SRGCmdLoadFromObject,
    SVP_20, __MIDL___MIDL_itf_sapi_0000_0020_0002, SPEI_RECOGNITION,
    DISPID_SOTCDefault, SAFT16kHz16BitStereo, SRATopLevel,
    ISpEventSource, SBOPause, SP_VISEME_19,
    DISPID_SRGCmdSetRuleIdState, SpeechAudioFormatGUIDWave,
    DISPID_SPRuleNumberOfElements, DISPID_SRGCommit,
    DISPID_SRCERecognizerStateChange, SGSEnabled, DISPID_SVPriority,
    SPSHORTCUTPAIRLIST, SPEI_REQUEST_UI, SGPronounciation,
    SPSHORTCUTPAIR, SpCompressedLexicon, SAFT12kHz8BitMono,
    SPRS_ACTIVE_WITH_AUTO_PAUSE, SSFMOpenReadWrite, SPSInterjection,
    SAFTCCITT_uLaw_22kHzStereo, DISPID_SRSetPropertyNumber,
    DISPID_SGRSTPropertyName, SAFT48kHz16BitMono, ISpeechRecognizer,
    SRSActive, DISPID_SRCEventInterests, SPEVENT,
    DISPID_SRAllowAudioInputFormatChangesOnNextSet, _FILETIME,
    ISpeechPhraseProperty, DISPID_SPPName, SPSMF_SRGS_SAPIPROPERTIES,
    SPPHRASEPROPERTY, DISPID_SRCState, SVP_8, ISpRecognizer,
    SRCS_Disabled, Speech_Default_Weight, DISPID_SPAsItem,
    ISpeechRecoContext, ISpLexicon, SSSPTRelativeToEnd,
    ISpGrammarBuilder, DISPID_SLPs_NewEnum, SVPOver, SP_VISEME_4,
    SP_VISEME_11, SPRECOGNIZERSTATUS, SVEPhoneme,
    SPEI_SENTENCE_BOUNDARY, DISPID_SLGenerationId,
    SAFT24kHz8BitStereo, DISPID_SRCRetainedAudio,
    DISPIDSPTSI_ActiveOffset, SAFT48kHz8BitStereo,
    SAFT44kHz8BitStereo, DISPID_SGRSTWeight, DISPID_SVSpeak,
    DISPID_SPEDisplayText, ISpPhrase, ISpeechAudioFormat,
    eLEXTYPE_RESERVED9, SAFT12kHz16BitStereo, SSFMCreateForWrite,
    SAFTCCITT_uLaw_8kHzMono, SP_VISEME_9, DISPID_SGRAddResource,
    DISPID_SASFreeBufferSpace, DISPID_SMSAMMHandle, ISpDataKey,
    SpeechAudioFormatGUIDText, DISPID_SRCreateRecoContext,
    SREHypothesis, ISpeechGrammarRule, ISpeechRecoResult2,
    DISPID_SPRulesItem, GUID, SPRST_INACTIVE, DISPID_SRIsShared,
    SAFT11kHz8BitMono, SPEI_START_SR_STREAM, eLEXTYPE_RESERVED10,
    SpeechDictationTopicSpelling, DISPID_SRRTTickCount,
    SRADefaultToActive, DISPIDSPTSI_SelectionLength, SVP_13,
    SpNotifyTranslator, ISpeechPhraseElement,
    DISPID_SRCCreateResultFromMemory, SPBINARYGRAMMAR,
    SAFTCCITT_ALaw_22kHzStereo, SAFTCCITT_ALaw_11kHzStereo,
    DISPID_SOTs_NewEnum, DISPID_SVEEnginePrivate, ISpeechVoice,
    SAFT24kHz8BitMono, SAFTCCITT_uLaw_8kHzStereo, SAFT8kHz8BitStereo,
    ISpStreamFormatConverter, DISPID_SAFGuid, SAFTADPCM_44kHzMono,
    eLEXTYPE_PRIVATE7, DISPID_SRSSupportedLanguages,
    DISPID_SVGetVoices, SpNullPhoneConverter, DISPID_SABIBufferSize,
    SVP_15, DISPID_SRRSpeakAudio, SPVOICESTATUS, DISPID_SOTDataKey,
    _RemotableHandle, SP_VISEME_15, DISPID_SPPsItem, SPSNoun,
    SAFTCCITT_uLaw_44kHzMono, SAFT48kHz16BitStereo,
    DISPID_SGRAddState, SP_VISEME_3, DISPID_SLWType, SPEI_SOUND_START,
    SVEVoiceChange, DISPID_SVDisplayUI,
    DISPID_SLRemovePronunciationByPhoneIds, ISpRecoResult,
    eLEXTYPE_PRIVATE6, SPGS_EXCLUSIVE, SpAudioFormat,
    SRERecoOtherContext, SPAUDIOBUFFERINFO, SGLexicalNoSpecialChars,
    SGDSInactive, DISPID_SRCPause, SpSharedRecognizer,
    SAFT32kHz16BitStereo, SGRSTTTextBuffer, SVP_21,
    DISPID_SDKGetlongValue, DISPID_SRCEAdaptation,
    DISPID_SPIAudioStreamPosition, SAFTGSM610_22kHzMono,
    SPRS_ACTIVE_USER_DELIMITED, ISpeechPhraseAlternates,
    DISPID_SLPsItem, SAFT8kHz16BitMono, ISpeechAudio,
    SSSPTRelativeToStart, SAFT11kHz8BitStereo, SRCS_Enabled, SFTInput,
    DISPID_SRCEStartStream, SAFTCCITT_ALaw_44kHzStereo,
    eLEXTYPE_PRIVATE4, DISPID_SRAudioInput, SREPropertyStringChange,
    DISPID_SOTsCount, SGRSTTDictation, SRAORetainAudio, SP_VISEME_20,
    SPPS_Noun, tagSPTEXTSELECTIONINFO, DISPID_SPELexicalForm,
    eLEXTYPE_LETTERTOSOUND, eLEXTYPE_PRIVATE8, DISPID_SMSADeviceId,
    DISPID_SVESentenceBoundary, DISPID_SLPType, SVSFPurgeBeforeSpeak,
    SAFTADPCM_8kHzStereo, SAFT24kHz16BitMono, DISPID_SPPValue,
    ISpRecoCategory, SVP_9, SPAR_Low, SAFT12kHz16BitMono,
    DISPID_SGRsItem, ISpeechPhoneConverter, SITooQuiet, SVP_19,
    SPEI_RECO_STATE_CHANGE, SRERequestUI, ISpRecognizer2,
    ISpXMLRecoResult, SPAUDIOSTATUS, SPSMF_SAPI_PROPERTIES,
    ISpeechPhraseInfoBuilder, SINone, DISPID_SPRsItem, Library,
    DISPID_SVGetProfiles, SpShortcut, SSTTDictation, DISPID_SVEWord,
    DISPID_SVEVoiceChange, DISPID_SRGetRecognizers,
    DISPID_SWFEChannels, DISPID_SABufferNotifySize, SLODynamic,
    ISpeechRecoResult, DISPID_SPPId, SpeechUserTraining, SPVPRI_OVER,
    SPEI_TTS_BOOKMARK, tagSPPROPERTYINFO, ISpRecoContext,
    SVSFParseMask, SVSFlagsAsync, IEnumSpObjectTokens,
    eLEXTYPE_PRIVATE5, SPWP_UNKNOWN_WORD_PRONOUNCEABLE,
    SPEI_ADAPTATION, eWORDTYPE_ADDED, DISPID_SPPsCount,
    DISPID_SPAPhraseInfo, SpInprocRecognizer, SPBO_TIME_UNITS,
    DISPID_SRCEPropertyNumberChange, SBONone, SP_VISEME_1,
    DISPID_SPEAudioTimeOffset, SPSMF_SRGS_SEMANTICINTERPRETATION_W3C,
    DISPID_SRGDictationLoad, SPSFunction, DISPID_SRCSetAdaptationData,
    SPRST_INACTIVE_WITH_PURGE, SPPS_RESERVED3,
    SpeechPropertyAdaptationOn, ISpRecoGrammar2, SVP_7,
    SRERecognition, SPPS_Modifier, DISPID_SOTGetAttribute,
    SP_VISEME_10, SDA_Consume_Leading_Spaces, SVSFPersistXML,
    DISPID_SVSRunningState, SPBO_PAUSE, SPWT_PRONUNCIATION,
    DISPID_SRGRules, STSF_FlagCreate, SASPause,
    IInternetSecurityMgrSite, DISPID_SRGCmdLoadFromProprietaryGrammar,
    SVEAudioLevel, DISPID_SRGRecoContext, ISpObjectTokenCategory,
    SPEI_FALSE_RECOGNITION, SDA_One_Trailing_Space, ISequentialStream,
    SRSInactive, SpeechTokenKeyFiles, SPEI_RESERVED3,
    DISPID_SGRSTs_NewEnum, DISPID_SRStatus, SAFT48kHz8BitMono,
    SVEWordBoundary, Speech_Max_Pron_Length,
    DISPID_SRCRetainedAudioFormat, SPAS_PAUSE, SECFIgnoreCase,
    SPFM_OPEN_READONLY, SP_VISEME_16, DISPID_SGRSTNextState,
    DISPID_SRCRecognizer, SPEI_SOUND_END,
    SpeechGrammarTagUnlimitedDictation, SpeechCategoryAudioIn,
    SPPS_SuppressWord, SDKLCurrentUser, SPINTERFERENCE_TOOLOUD,
    DISPID_SPAs_NewEnum, DISPID_SPIStartTime,
    SPWP_KNOWN_WORD_PRONOUNCEABLE, SP_VISEME_0, ISpRecoGrammar,
    DISPID_SADefaultFormat, SPEI_SR_PRIVATE,
    SpeechPropertyNormalConfidenceThreshold, DISPID_SWFEExtraData,
    DISPID_SPPEngineConfidence, SAFT22kHz8BitMono, SPPS_Interjection,
    SVP_4, DISPID_SRCVoicePurgeEvent, SPAO_NONE, STCLocalServer,
    SAFT22kHz8BitStereo, SAFTADPCM_22kHzMono,
    DISPID_SPPBRestorePhraseFromMemory, ISpeechPhraseProperties,
    SPAS_RUN, SPAS_CLOSED, DISPID_SOTCEnumerateTokens,
    SVSFParseAutodetect, SPSSuppressWord, SPEI_MIN_TTS,
    DISPID_SGRSTsItem, SAFTCCITT_ALaw_8kHzStereo, eLEXTYPE_RESERVED8,
    ISpeechAudioStatus, SPSEMANTICERRORINFO, ISpObjectWithToken,
    DISPID_SAVolume, DISPID_SPIReplacements, SREPropertyNumChange,
    SPSERIALIZEDRESULT, ISpNotifySource, DISPID_SCSBaseStream,
    DISPID_SDKCreateKey, ISpPhraseAlt,
    DISPID_SRAllowVoiceFormatMatchingOnNextSet, SPEI_HYPOTHESIS,
    DISPID_SGRSTPropertyValue, SPRECOCONTEXTSTATUS, CoClass,
    SECLowConfidence, SPEI_SR_BOOKMARK, ISpeechGrammarRules,
    SASClosed, DISPID_SPIAudioSizeTime, SpeechCategoryPhoneConverters,
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS, DISPID_SOTCGetDataKey,
    SRTReSent, DISPID_SRCEEndStream, SGRSTTEpsilon, STCInprocHandler,
    DISPID_SLRemovePronunciation, DISPID_SRRRecoContext,
    SPWT_LEXICAL_NO_SPECIAL_CHARS, eLEXTYPE_MORPHOLOGY,
    IInternetSecurityManager, SAFTText,
    SpeechPropertyComplexResponseSpeed, DISPID_SVEAudioLevel,
    SpSharedRecoContext, DISPIDSPTSI_SelectionOffset, SPAR_Medium,
    eLEXTYPE_APP, SpeechTokenValueCLSID, SWPUnknownWordPronounceable,
    SPEI_INTERFERENCE, DISPID_SRRSetTextFeedback, SPFM_OPEN_READWRITE,
    SGRSTTWildcard, SVF_Stressed, SVSFDefault, DISPID_SMSALineId,
    DISPID_SPAsCount, DISPID_SABIMinNotification,
    DISPID_SPIRetainedSizeBytes, SAFTCCITT_ALaw_8kHzMono,
    ISpObjectToken, SGDSActive, SAFT22kHz16BitMono, SPPS_Function,
    SRARoot, DISPID_SPIRule, SP_VISEME_12, eLEXTYPE_PRIVATE20,
    SP_VISEME_5, SAFT24kHz16BitStereo, SWPKnownWordPronounceable,
    SDTAlternates, DISPID_SBSFormat, SPEI_RESERVED5,
    DISPID_SPARecoResult, DISPID_SVSCurrentStreamNumber,
    DISPID_SAStatus, ISpVoice, SDKLDefaultLocation, SRTEmulated,
    ISpeechPhraseElements, DISPID_SPILanguageId, SAFTADPCM_8kHzMono,
    DISPID_SVGetAudioOutputs, SPSHT_Unknown, SVPAlert,
    DISPID_SAFGetWaveFormatEx, _lcid, SPEI_VOICE_CHANGE,
    DISPID_SPPParent, DISPID_SRRAudioFormat, SAFT44kHz8BitMono,
    DISPID_SRSNumberOfActiveRules, DISPID_SPAStartElementInResult,
    DISPID_SPEPronunciation, ISpeechRecognizerStatus,
    DISPID_SDKSetLongValue, DISPID_SVWaitUntilDone,
    SAFTCCITT_ALaw_22kHzMono, DISPID_SGRsDynamic,
    ISpPhoneticAlphabetConverter, SDKLCurrentConfig, SSTTTextBuffer,
    DISPID_SVSpeakCompleteEvent, DISPID_SRCEPhraseStart,
    DISPID_SPRsCount, SP_VISEME_2, SP_VISEME_18, DISPID_SRRAudio,
    SRTSMLTimeout, DISPID_SLGetPronunciations, DISPID_SRRTStreamTime,
    SRSEDone, DISPID_SPIGetDisplayAttributes, SAFT16kHz8BitStereo,
    eLEXTYPE_PRIVATE10, ISpeechVoiceStatus, SPWT_DISPLAY, SpVoice,
    SpFileStream, SVP_2, SPSLMA, DISPID_SOTIsUISupported,
    DISPID_SVSVisemeId, SpeechGrammarTagWildcard, DISPID_SOTCategory,
    SPEI_MAX_TTS, SP_VISEME_6, DISPID_SGRSTType,
    DISPID_SRCCreateGrammar, SAFT32kHz8BitStereo, SECFDefault
)


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE
SPAUDIOSTATE = _SPAUDIOSTATE


__all__ = [
    'SPVPRI_NORMAL', 'SPTEXTSELECTIONINFO', 'STCInprocServer',
    'ISpShortcut', 'DISPID_SPEsCount', 'DISPID_SVEventInterests',
    'SAFT32kHz8BitMono', 'SPRECOSTATE', 'SpMMAudioEnum',
    'SpeechVoiceEvents', 'SPEI_RESERVED6', 'SPEI_MIN_SR',
    'eLEXTYPE_PRIVATE13', 'DISPID_SASCurrentSeekPosition',
    'SPSTREAMFORMATTYPE', 'DISPID_SDKEnumKeys', 'SLTApp',
    'ISpEventSink', 'SP_VISEME_17', 'DISPID_SVAudioOutputStream',
    'DISPID_SLAddPronunciation', 'SREPrivate', 'SDTAudio',
    'DISPID_SPEs_NewEnum', 'DISPID_SPIElements', 'SpeechFormatType',
    'SpeechDisplayAttributes', 'SpMMAudioOut',
    'DISPID_SOTGetDescription', 'STSF_LocalAppData',
    'DISPID_SPIEngineId', 'SPRST_ACTIVE', 'DISPID_SRCERequestUI',
    'DISPIDSPTSI', 'DISPID_SRRPhraseInfo', 'SPPS_RESERVED1',
    'SFTSREngine', 'SpeechAudioVolume', 'SPPHRASERULE',
    'DISPID_SOTRemove', 'SPPROPERTYINFO', 'SRESoundEnd',
    'SPRST_NUM_STATES', 'SPCATEGORYTYPE', 'SpObjectToken',
    'DISPID_SOTCSetId', 'SRSEIsSpeaking', 'SVP_5', 'DISPID_SPACommit',
    'SPVPRI_ALERT', 'DISPID_SPIProperties',
    'DISPID_SREmulateRecognition', 'ISpSerializeState',
    'SAFT32kHz16BitMono', 'SAFTGSM610_44kHzMono', 'DISPID_SRGState',
    'DISPID_SVVolume', 'DISPID_SRSetPropertyString',
    'eLEXTYPE_PRIVATE19', 'SVP_3', 'DISPID_SLWPronunciations',
    'SAFTGSM610_8kHzMono', 'ISpeechGrammarRuleState',
    'DISPID_SRCEBookmark', 'DISPID_SLAddPronunciationByPhoneIds',
    'SPVISEMES', 'SpeechGrammarRuleStateTransitionType',
    'SpPhoneConverter', 'SP_VISEME_7', 'ISpeechRecoGrammar',
    'DISPID_SABIEventBias', 'ISpRecognizer3', 'SRADynamic',
    'SSFMOpenForRead', 'eLEXTYPE_RESERVED6', 'SPWORDLIST',
    'eLEXTYPE_PRIVATE14', 'DISPID_SPEAudioSizeBytes',
    'SpeechAudioFormatType', 'DISPID_SRRGetXMLErrorInfo',
    'SSFMCreate', 'DISPID_SAFSetWaveFormatEx', 'SP_VISEME_21',
    'SPXRO_Alternates_SML', 'SpeechTokenIdUserLexicon', 'SASRun',
    'DISPID_SAEventHandle', 'SSSPTRelativeToCurrentPosition',
    'SAFTNoAssignedFormat', 'DISPID_SpeechVoiceEvent',
    'SpPhraseInfoBuilder', 'SPFM_CREATE', 'DISPID_SRSAudioStatus',
    'DISPID_SWFEBlockAlign', 'DISPID_SVSkip', 'eLEXTYPE_PRIVATE3',
    'SRESoundStart', 'SPRECORESULTTIMES', 'DISPID_SGRsCount',
    'SpeechTokenKeyUI', 'SVP_14', 'DISPID_SPRules_NewEnum',
    'SpTextSelectionInformation', 'SVP_1',
    'DISPID_SRGIsPronounceable', 'SpeechRecoEvents', 'SINoSignal',
    'SDTRule', 'SDTLexicalForm', 'tagSTATSTG', 'DISPID_SRCBookmark',
    'ISpProperties', 'DISPID_SRGCmdSetRuleState',
    'SpeechSpecialTransitionType', 'SPWORD', 'SpeechMicTraining',
    'DISPID_SVEStreamStart', 'SAFT11kHz16BitMono',
    'eLEXTYPE_PRIVATE17', 'DISPID_SDKDeleteValue',
    'SPPHRASEREPLACEMENT', 'DISPID_SpeechRecoResult',
    'DISPID_SBSWrite', 'ISpeechTextSelectionInformation',
    'DISPID_SMSGetData', 'DISPID_SRSCurrentStreamPosition',
    'DISPID_SVPause', 'ISpeechObjectToken',
    'DISPID_SRGDictationUnload', 'SpeechDiscardType', 'ISpAudio',
    'eLEXTYPE_USER', 'SPWF_INPUT', 'SGRSTTRule', 'SSTTWildcard',
    'SRAInterpreter', 'SPGRAMMARSTATE', 'ISpeechMMSysAudio',
    'SpeechRunState', 'DISPID_SLWWord', 'DISPID_SRCVoice',
    'SPAUDIOOPTIONS', 'SpObjectTokenCategory', 'SRAONone',
    'SITooSlow', 'DISPID_SRGCmdLoadFromObject', 'SDTPronunciation',
    'DISPID_SGRSTPropertyId', 'SVP_20',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002', 'SPEI_RECOGNITION',
    'DISPID_SOTCDefault', 'DISPID_SRAudioInputStream',
    'SAFT16kHz16BitStereo', 'SRATopLevel', 'SDA_Two_Trailing_Spaces',
    'ISpEventSource', 'SBOPause', 'SP_VISEME_19', 'DISPID_SPEsItem',
    'DISPID_SPIEnginePrivateData', 'DISPID_SRGCmdSetRuleIdState',
    'SpeechRecognitionType', 'SPFM_CREATE_ALWAYS',
    'SpeechAudioFormatGUIDWave', 'STCRemoteServer',
    'DISPID_SLPsCount', 'SpeechVoiceSpeakFlags', 'SpWaveFormatEx',
    'DISPID_SPRuleNumberOfElements', 'SPPS_RESERVED2',
    'DISPID_SRGCommit', 'DISPID_SRCERecognizerStateChange',
    'SGSEnabled', 'DISPID_SVPriority', 'SPSHORTCUTPAIRLIST',
    'DISPID_SpeechBaseStream', 'SpeechLoadOption', 'SPEI_REQUEST_UI',
    'SGPronounciation', 'SPSHORTCUTPAIR', 'DISPID_SVVoice',
    'ISpeechDataKey', 'SpeechRuleAttributes', 'SpCompressedLexicon',
    'SAFT12kHz8BitMono', 'SVP_6', 'SVESentenceBoundary',
    'SPRS_ACTIVE_WITH_AUTO_PAUSE', 'SSFMOpenReadWrite',
    'SPWAVEFORMATTYPE', 'DISPID_SRCERecognition', 'SRTAutopause',
    'SPSInterjection', 'DISPID_SRCEPropertyStringChange',
    'SAFTCCITT_uLaw_22kHzStereo', 'WAVEFORMATEX',
    'SAFTCCITT_uLaw_11kHzStereo', 'DISPID_SRSetPropertyNumber',
    'DISPID_SGRSTPropertyName', 'SAFT48kHz16BitMono',
    'ISpeechResourceLoader', 'DISPID_SPEAudioSizeTime',
    'ISpeechRecognizer', 'SRSActive', 'DISPID_SpeechPhraseElements',
    'DISPID_SRCRequestedUIType', 'DISPID_SRCEventInterests',
    'SPEVENT', 'SAFTTrueSpeech_8kHz1BitMono', 'SPLOADOPTIONS',
    'SAFTADPCM_22kHzStereo', 'DISPID_SRCESoundStart',
    'ISpeechGrammarRuleStateTransitions',
    'DISPID_SRAllowAudioInputFormatChangesOnNextSet',
    'DISPID_SRRTimes', 'DISPID_SRCEEnginePrivate',
    'DISPID_SpeechObjectToken', 'DISPID_SRSClsidEngine',
    'ISpeechPhraseProperty', 'DISPID_SPIGrammarId', 'DISPID_SASState',
    'DISPID_SPPName', 'SPSMF_SRGS_SAPIPROPERTIES',
    'SAFT11kHz16BitStereo', 'SPPHRASEPROPERTY', 'DISPID_SRCState',
    'SVP_8', 'DISPID_SRCEInterference', 'SpeechRecognizerState',
    'ISpRecognizer', 'SRCS_Disabled', 'Speech_Default_Weight',
    'DISPID_SVStatus', 'DISPID_SpeechVoice', 'DISPID_SRCESoundEnd',
    'DISPID_SPAsItem', 'ISpeechRecoContext', 'DISPID_SRRAlternates',
    'ISpLexicon', 'SPSMF_UPS', 'SPEI_PHONEME', 'SSSPTRelativeToEnd',
    'ISpGrammarBuilder', 'DISPID_SLPs_NewEnum', 'eLEXTYPE_PRIVATE11',
    'DISPID_SpeechAudio', 'SVPOver', 'SPSHT_EMAIL', 'SP_VISEME_4',
    'SP_VISEME_11', 'DISPID_SRSCurrentStreamNumber',
    'DISPID_SDKDeleteKey', 'SPRECOGNIZERSTATUS', 'SVEPhoneme',
    'SRAImport', '_ISpeechRecoContextEvents',
    'DISPID_SRCCmdMaxAlternates', 'SPINTERFERENCE_NONE',
    'SPEI_SENTENCE_BOUNDARY', 'eLEXTYPE_PRIVATE15',
    'DISPID_SLGenerationId', 'STCAll', 'ISpStream', 'SVSFVoiceMask',
    'DISPID_SBSSeek', 'SAFT24kHz8BitStereo', 'DISPID_SPCIdToPhone',
    'DISPID_SRCRetainedAudio', 'DISPIDSPTSI_ActiveOffset',
    'SAFT48kHz8BitStereo', 'SAFT44kHz8BitStereo', 'SpLexicon',
    'DISPIDSPTSI_ActiveLength', 'ISpNotifySink', 'DISPID_SGRsAdd',
    'DISPID_SGRSTWeight', '__MIDL_IWinTypes_0009', 'DISPID_SVSpeak',
    'DISPID_SPCLangId', 'DISPID_SPEDisplayText', 'ISpPhrase',
    'DISPID_SpeechDataKey', 'DISPID_SpeechAudioFormat',
    'SAFTADPCM_11kHzMono', 'ISpeechAudioFormat', 'eLEXTYPE_RESERVED9',
    'SPSNotOverriden', 'DISPID_SpeechCustomStream',
    'ISpeechObjectTokens', 'DISPID_SVSLastResult',
    'SAFT12kHz16BitStereo', 'SSFMCreateForWrite',
    'SAFTCCITT_uLaw_8kHzMono', 'SPEI_PROPERTY_STRING_CHANGE',
    'SP_VISEME_9', 'DISPID_SOTRemoveStorageFileName',
    'SREStateChange', 'DISPID_SGRAddResource',
    'DISPID_SASFreeBufferSpace', 'DISPID_SpeechPhraseProperties',
    'DISPID_SMSAMMHandle', 'SREInterference',
    'DISPID_SpeechAudioStatus', 'ISpDataKey',
    'SpeechAudioFormatGUIDText', 'DISPID_SRCreateRecoContext',
    'SREHypothesis', 'ISpeechGrammarRule', 'SPSUnknown',
    'SDTProperty', 'DISPID_SOTMatchesAttributes',
    'ISpeechRecoResult2', 'SGSExclusive', 'DISPID_SPRulesItem',
    'SDKLLocalMachine', 'SP_VISEME_13', 'SVEPrivate',
    'SPRST_INACTIVE', 'Speech_StreamPos_Asap', 'eWORDTYPE_DELETED',
    'DISPID_SRIsShared', 'SpeechTokenShellFolder',
    'SAFT11kHz8BitMono', 'DISPID_SVSLastBookmarkId',
    'SPEI_START_SR_STREAM', 'eLEXTYPE_RESERVED10',
    'DISPID_SPRuleFirstElement', 'SpeechDictationTopicSpelling',
    'DISPID_SASNonBlockingIO', 'SpeechAudioProperties',
    'ISpRecoContext2', 'DISPID_SRRTTickCount', 'SRADefaultToActive',
    'SAFTExtendedAudioFormat', 'SpeechEmulationCompareFlags',
    'SpMemoryStream', 'DISPIDSPTSI_SelectionLength', 'SVP_13',
    'DISPID_SpeechLexiconWords', 'SpeechRegistryLocalMachineRoot',
    'DISPID_SPERetainedStreamOffset', 'IStream', 'SPWORDTYPE',
    'DISPID_SRRTOffsetFromStart', 'SpNotifyTranslator', 'SREBookmark',
    'ISpeechPhraseElement', 'DISPID_SRGDictationSetState',
    'SPEI_RESERVED1', 'DISPID_SRCCreateResultFromMemory',
    'SpeechDataKeyLocation', 'DISPID_SVSInputSentenceLength',
    'SPBINARYGRAMMAR', 'SAFTCCITT_ALaw_22kHzStereo',
    'SpeechRecoProfileProperties', 'ISpeechPhraseReplacements',
    'DISPID_SpeechRecoContextEvents', 'SLTUser',
    'SAFTCCITT_ALaw_11kHzStereo', 'DISPID_SVAlertBoundary',
    'SPPHRASE', 'DISPID_SOTs_NewEnum', 'DISPID_SVEEnginePrivate',
    'ISpeechVoice', 'SAFT24kHz8BitMono', 'DISPID_SDKSetBinaryValue',
    'DISPID_SpeechVoiceStatus', 'SAFTCCITT_uLaw_8kHzStereo',
    'SREStreamEnd', 'SAFTCCITT_ALaw_11kHzMono',
    'DISPID_SpeechGrammarRuleState', 'DISPID_SPRuleName',
    'SpeechEngineProperties', 'DISPID_SWFEAvgBytesPerSec',
    'SAFT8kHz8BitStereo', 'ISpStreamFormatConverter',
    'DISPID_SAFGuid', 'SAFTADPCM_44kHzMono', 'SVEEndInputStream',
    'SpCustomStream', 'ISpMMSysAudio', 'eLEXTYPE_PRIVATE7',
    'DISPID_SRSSupportedLanguages', 'DISPID_SpeechPhraseRule',
    'DISPID_SVGetVoices', 'ISpeechLexiconPronunciation',
    'DISPID_SVSInputWordPosition', 'SPEI_ACTIVE_CATEGORY_CHANGED',
    'DISPID_SRRDiscardResultInfo', 'SPXMLRESULTOPTIONS',
    'SpNullPhoneConverter', 'DISPID_SABIBufferSize', 'SVP_17',
    'DISPID_SRGCmdLoadFromFile', 'DISPID_SGRsCommit',
    'DISPID_SpeechPhraseProperty', 'SVP_0', 'SPGS_ENABLED', 'SVP_15',
    'SpeechWordType', 'DISPID_SRGetFormat', 'DISPID_SRDisplayUI',
    'DISPID_SRRSpeakAudio', 'DISPID_SpeechMemoryStream',
    'SPEVENTENUM', 'SPVOICESTATUS', 'SPEI_WORD_BOUNDARY',
    'DISPID_SpeechLexiconProns', 'DISPID_SOTDataKey',
    'DISPID_SFSOpen', 'SVP_18', 'DISPID_SLWs_NewEnum', 'SPXRO_SML',
    'DISPID_SPRText', 'DISPID_SGRId', 'SAFTDefault',
    'DISPID_SPPFirstElement', '_RemotableHandle', 'SP_VISEME_15',
    'DISPID_SPPs_NewEnum', 'ISpeechCustomStream', 'DISPID_SPPsItem',
    'SPSNoun', 'DISPID_SRCResume', 'SAFTCCITT_uLaw_44kHzMono',
    'SPRULE', 'SpeechCategoryAppLexicons', 'SAFT48kHz16BitStereo',
    'ISpResourceManager', 'DISPID_SGRAddState', 'SP_VISEME_8',
    'SPSModifier', 'SP_VISEME_3', 'DISPID_SLWType',
    'DISPID_SABufferInfo', 'DISPID_SpeechPhraseAlternates',
    'SpStreamFormatConverter', 'SLOStatic', 'SPEI_SOUND_START',
    'DISPID_SVRate', 'ISpeechPhraseInfo', 'SVEVoiceChange',
    'DISPID_SVDisplayUI', 'DISPID_SRRecognizer',
    'DISPID_SDKGetStringValue',
    'DISPID_SLRemovePronunciationByPhoneIds', 'ISpRecoResult',
    'eLEXTYPE_PRIVATE6', 'SPCT_SUB_COMMAND', 'SpeechStreamFileMode',
    'DISPID_SDKGetBinaryValue', 'SPGS_EXCLUSIVE',
    'STSF_CommonAppData', 'DISPID_SVSInputWordLength',
    'DISPID_SLWsItem', 'SPDATAKEYLOCATION', 'SpAudioFormat',
    'SPEI_PHRASE_START', 'SPEI_END_SR_STREAM', 'SRERecoOtherContext',
    'SPAUDIOBUFFERINFO', 'DISPID_SOTSetId',
    'DISPID_SpeechLexiconPronunciation', 'SpeechRegistryUserRoot',
    'SGLexicalNoSpecialChars', 'SGDSInactive', 'DISPID_SRCPause',
    'DISPID_SWFEFormatTag', 'SpeechTokenContext',
    'SpSharedRecognizer', 'SAFT32kHz16BitStereo', 'SGRSTTTextBuffer',
    'SVP_21', 'SAFTCCITT_uLaw_22kHzMono', 'DISPID_SPRulesCount',
    'DISPID_SpeechPhraseReplacements', 'DISPID_SDKGetlongValue',
    'DISPID_SMSSetData', 'DISPID_SRCEAdaptation',
    'DISPID_SPIAudioStreamPosition', 'SAFTGSM610_22kHzMono',
    'SDA_No_Trailing_Space', 'SPRS_ACTIVE_USER_DELIMITED',
    'ISpeechPhraseAlternates', 'DISPID_SLPsItem', 'SAFT8kHz16BitMono',
    'SPRULESTATE', 'SVSFUnusedFlags', 'ISpeechAudio',
    'SSSPTRelativeToStart', 'SAFT11kHz8BitStereo', 'SRCS_Enabled',
    'SAFT8kHz16BitStereo', 'DISPID_SWFEBitsPerSample',
    'SPEI_SR_AUDIO_LEVEL', 'DISPID_SPEDisplayAttributes', 'SFTInput',
    'SVF_Emphasis', 'DISPID_SFSClose', 'SPAR_Unknown',
    'DISPID_SRCEStartStream', 'SAFTCCITT_ALaw_44kHzStereo',
    '_ISpeechVoiceEvents', 'eLEXTYPE_PRIVATE4', 'DISPID_SRAudioInput',
    'SpeechPartOfSpeech', 'DISPID_SPERequiredConfidence',
    'DISPID_SpeechGrammarRules', 'SREPropertyStringChange',
    'ISpeechPhraseAlternate', 'DISPID_SOTsCount', 'SGRSTTDictation',
    'SRAORetainAudio', 'SP_VISEME_20', 'SPPS_Noun',
    'tagSPTEXTSELECTIONINFO', 'eLEXTYPE_PRIVATE12',
    'DISPID_SPIGetText', 'SREStreamStart',
    'SpeechCategoryRecoProfiles', 'SPBO_AHEAD', 'SDTDisplayText',
    'eLEXTYPE_LETTERTOSOUND', 'DISPID_SGRSTRule', 'eLEXTYPE_PRIVATE8',
    'DISPID_SPELexicalForm', 'DISPID_SOTId', 'DISPID_SMSADeviceId',
    'SAFTCCITT_uLaw_44kHzStereo', 'DISPID_SVESentenceBoundary',
    'SP_VISEME_14', 'SPEI_RECO_OTHER_CONTEXT', 'eLEXTYPE_PRIVATE18',
    'DISPID_SpeechXMLRecoResult', 'DISPID_SLPType',
    'SpeechVisemeFeature', 'SVSFPurgeBeforeSpeak',
    'SPDKL_CurrentConfig', 'DISPID_SVResume', 'SAFTADPCM_8kHzStereo',
    'DISPID_SLGetWords', 'DISPID_SRCEHypothesis', 'DISPID_SDKOpenKey',
    'DISPID_SGRSTText', 'DISPID_SPPValue',
    'DISPID_SASCurrentDevicePosition', 'ISpRecoCategory', 'SVP_9',
    'SPAR_Low', 'SECFIgnoreWidth', 'DISPID_SpeechRecoContext',
    'SAFT12kHz16BitMono', 'DISPID_SpeechPhraseBuilder', 'SGSDisabled',
    'DISPID_SRIsUISupported', 'SGDSActiveUserDelimited',
    'DISPID_SGRsItem', 'ISpeechPhoneConverter', 'SpInProcRecoContext',
    'SITooQuiet', 'SVSFNLPMask', 'SpeechVoiceCategoryTTSRate',
    'SVP_19', 'DISPID_SpeechLexicon', 'DISPID_SLWsCount',
    'SPEI_RECO_STATE_CHANGE', 'SRERequestUI', 'eLEXTYPE_PRIVATE16',
    'ISpRecognizer2', 'SAFT44kHz16BitMono', 'SAFTGSM610_11kHzMono',
    'ISpXMLRecoResult', 'SPAUDIOSTATUS', 'DISPID_SOTsItem',
    'SPINTERFERENCE_NOSIGNAL', 'SPSMF_SAPI_PROPERTIES',
    'DISPID_SLPLangId', 'SASStop', 'DISPID_SGRSAddSpecialTransition',
    'ISpeechPhraseInfoBuilder', 'DISPID_SpeechWaveFormatEx',
    'DISPID_SVEPhoneme', 'SPWT_LEXICAL', 'SINone', 'DISPID_SPRsItem',
    'DISPID_SVGetAudioInputs', 'ISpeechPhraseReplacement',
    'SPCS_DISABLED', 'SPEVENTSOURCEINFO', 'DISPID_SDKSetStringValue',
    'DISPID_SpeechRecognizer', 'DISPID_SGRSTransitions',
    'DISPID_SAFType', 'Library', 'DISPID_SRGetPropertyNumber',
    'DISPID_SVGetProfiles', 'SpPhoneticAlphabetConverter',
    'SpShortcut', 'SVP_10', 'SREAllEvents', 'DISPID_SPPConfidence',
    'ISpPhoneConverter', 'SSTTDictation', 'DISPID_SVEWord',
    'DISPID_SGRs_NewEnum', 'ISpeechRecoResultTimes',
    'DISPID_SPRNumberOfElements', 'ISpeechRecoResultDispatch',
    'DISPID_SVEVoiceChange', 'SGRSTTWord', 'DISPID_SVSPhonemeId',
    'DISPID_SRGetRecognizers', 'SpResourceManager', 'SPFM_NUM_MODES',
    'DISPID_SWFEChannels', 'DISPID_SABufferNotifySize',
    'DISPID_SpeechGrammarRule', 'ISpeechXMLRecoResult', 'SGLexical',
    'SLODynamic', 'DISPID_SRGCmdLoadFromMemory', 'ISpeechRecoResult',
    'DISPID_SGRsCommitAndSave', 'DISPID_SRCAudioInInterferenceStatus',
    'DISPID_SPPId', 'SpeechGrammarTagDictation', 'SpeechUserTraining',
    'SPLO_STATIC', 'SPPS_Verb', 'SPVPRI_OVER', 'eLEXTYPE_RESERVED7',
    'SPEI_TTS_BOOKMARK', 'tagSPPROPERTYINFO', 'eLEXTYPE_PRIVATE9',
    'ISpRecoContext', 'SITooLoud', 'SVSFParseMask',
    'SAFTCCITT_uLaw_11kHzMono', 'SVSFlagsAsync',
    'IEnumSpObjectTokens', 'eLEXTYPE_PRIVATE5',
    'SpeechRetainedAudioOptions', 'SPRS_ACTIVE',
    'SPWP_UNKNOWN_WORD_PRONOUNCEABLE', 'SVEStartInputStream',
    'DISPID_SpeechRecognizerStatus', 'DISPID_SRRTLength',
    'eWORDTYPE_ADDED', 'DISPID_SPIAudioSizeBytes', 'SPEI_ADAPTATION',
    'DISPID_SPPsCount', 'SPEI_RESERVED2', 'ISpStreamFormat',
    'SPCT_DICTATION', 'DISPIDSPRG', 'DISPID_SPAPhraseInfo',
    'DISPID_SOTGetStorageFileName', 'SpInprocRecognizer',
    'SPBO_TIME_UNITS', 'SAFT44kHz16BitStereo',
    'DISPID_SRCEPropertyNumberChange', 'SpUnCompressedLexicon',
    'SBONone', 'SPCT_SLEEP', 'DISPID_SGRInitialState',
    'DISPID_SpeechObjectTokens', 'SPAUDIOSTATE', 'SP_VISEME_1',
    'SAFTCCITT_ALaw_44kHzMono', 'DISPID_SPRuleConfidence',
    'DISPID_SPEAudioTimeOffset', 'SpeechCategoryRecognizers',
    'SRSActiveAlways', 'DISPID_SRGDictationLoad', 'SPSFunction',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'eLEXTYPE_PRIVATE1',
    'SAFT32kHz8BitStereo', 'SPPS_Noncontent', 'SpeechCategoryVoices',
    'SAFTADPCM_11kHzStereo', 'DISPID_SPEAudioStreamOffset',
    'DISPID_SRCSetAdaptationData',
    'SpeechPropertyLowConfidenceThreshold', 'DISPID_SRGId',
    'SPRST_INACTIVE_WITH_PURGE', 'DISPID_SVEViseme',
    'DISPID_SPRuleChildren', 'SPPS_RESERVED3',
    'SpeechPropertyAdaptationOn', 'SRTStandard', 'SVP_7',
    'SpeechGrammarWordType', 'ISpRecoGrammar2', 'SPPS_Modifier',
    'SRERecognition', 'DISPID_SOTGetAttribute', 'DISPID_SOTDisplayUI',
    'SpeechBookmarkOptions', 'SPSEMANTICFORMAT', 'SP_VISEME_10',
    'SDA_Consume_Leading_Spaces', 'SVSFPersistXML',
    'DISPID_SVSRunningState', 'SPBO_PAUSE', 'SPWT_PRONUNCIATION',
    'DISPID_SRGRules', 'DISPID_SpeechMMSysAudio',
    'DISPID_SRCEAudioLevel', 'SWTAdded', 'SAFT22kHz16BitStereo',
    'DISPID_SPANumberOfElementsInResult', '_SPAUDIOSTATE',
    'ISpeechAudioBufferInfo', 'SINoise', 'STSF_FlagCreate',
    'DISPID_SPRuleParent', 'ISpeechLexicon', 'eLEXTYPE_USER_SHORTCUT',
    'SpeechCategoryAudioOut', 'SASPause', 'DISPID_SVIsUISupported',
    'IInternetSecurityMgrSite', 'SVEAllEvents',
    'DISPID_SRGetPropertyString', 'DISPID_SRGReset',
    'DISPID_SRGSetTextSelection',
    'DISPID_SRGCmdLoadFromProprietaryGrammar', 'SVEAudioLevel',
    'ISpeechLexiconWord', 'DISPID_SRGRecoContext',
    'ISpObjectTokenCategory', 'ISpeechLexiconPronunciations',
    'SPEI_FALSE_RECOGNITION', 'DISPID_SpeechPhraseRules',
    'SPINTERFERENCE_TOOQUIET', 'SRSInactiveWithPurge',
    'SPWORDPRONUNCIATIONLIST', 'SDA_One_Trailing_Space',
    'SPSHT_OTHER', 'SRSInactive', 'SpeechTokenKeyFiles', 'SPAR_High',
    'SPEI_RESERVED3', 'DISPID_SGRSTs_NewEnum', 'DISPID_SRStatus',
    'SPAS_STOP', 'SAFT48kHz8BitMono', 'SVEWordBoundary',
    'SPEI_SR_RETAINEDAUDIO', 'Speech_Max_Pron_Length',
    'SpeechEngineConfidence', 'SPWF_SRENGINE',
    'DISPID_SRCRetainedAudioFormat', 'DISPID_SPPChildren',
    'SPLO_DYNAMIC', 'SECFIgnoreKanaType', 'SPAS_PAUSE',
    'SECFIgnoreCase', 'DISPID_SWFESamplesPerSec',
    'SPFM_OPEN_READONLY', 'DISPID_SGRAttributes',
    'ISpNotifyTranslator', 'SP_VISEME_16', 'DISPID_SRRGetXMLResult',
    'DISPID_SRGCmdLoadFromResource',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN', 'DISPID_SGRSTNextState',
    'SREFalseRecognition', 'SpMMAudioIn', 'DISPID_SRCRecognizer',
    'SPEI_SOUND_END', 'DISPID_SLWLangId',
    'SpeechGrammarTagUnlimitedDictation', 'SpeechCategoryAudioIn',
    'SPPS_NotOverriden', 'SDKLCurrentUser', 'DISPID_SLPSymbolic',
    'SPPS_SuppressWord', 'DISPID_SpeechPhraseReplacement',
    'DISPID_SPRuleId', 'SPINTERFERENCE_TOOLOUD',
    'DISPID_SPAs_NewEnum', 'IEnumString', 'ISpeechWaveFormatEx',
    'SAFTNonStandardFormat', 'DISPID_SPIStartTime', 'SPEI_MAX_SR',
    'SGDSActiveWithAutoPause', 'SPWP_KNOWN_WORD_PRONOUNCEABLE',
    'SPCT_SUB_DICTATION', 'SP_VISEME_0', 'SAFT8kHz8BitMono',
    'SpeechPropertyResponseSpeed', 'ISpRecoGrammar',
    'ISpeechGrammarRuleStateTransition', 'DISPID_SADefaultFormat',
    'SpeechAudioState', 'SpeechPropertyNormalConfidenceThreshold',
    'DISPID_SPRDisplayAttributes', 'SPEI_SR_PRIVATE',
    'DISPID_SLPPartOfSpeech', 'SRTExtendableParse',
    'SPEI_TTS_PRIVATE', 'DISPID_SWFEExtraData',
    'DISPID_SPPEngineConfidence', 'SPINTERFERENCE_LATENCY_WARNING',
    'SPSVerb', 'SAFT22kHz8BitMono', 'SPPS_Interjection', 'SVP_4',
    'SpeechStreamSeekPositionType', 'DISPID_SRCVoicePurgeEvent',
    'SpeechAddRemoveWord', 'SPAO_NONE', 'DISPID_SPEEngineConfidence',
    'STCLocalServer', 'DISPID_SRRSaveToMemory', 'SAFT22kHz8BitStereo',
    'SAFTADPCM_22kHzMono', 'SpeechWordPronounceable',
    'ISpeechLexiconWords', 'SPADAPTATIONRELEVANCE',
    'DISPID_SPPBRestorePhraseFromMemory', 'ISpeechPhraseProperties',
    'SPAS_RUN', '__MIDL___MIDL_itf_sapi_0000_0020_0001',
    'SPEI_END_INPUT_STREAM', 'SVSFParseSsml', 'SECFNoSpecialChars',
    'SpeechRuleState', 'SPINTERFERENCE_TOOSLOW',
    'DISPID_SPPNumberOfElements', 'ISpeechBaseStream', 'typelib_path',
    'SPAS_CLOSED', 'DISPID_SPRFirstElement', 'SVP_12',
    'SPWORDPRONOUNCEABLE', 'DISPID_SpeechPhoneConverter', 'SVEViseme',
    'DISPID_SOTCEnumerateTokens', 'SPEI_TTS_AUDIO_LEVEL',
    'SVSFParseAutodetect', 'SPSSuppressWord',
    'SPEI_PROPERTY_NUM_CHANGE', 'DISPID_SOTCId',
    'SpeechVoicePriority', 'SPEI_MIN_TTS', 'DISPID_SGRSTsItem',
    'SAFTCCITT_ALaw_8kHzStereo',
    'DISPID_SRCERecognitionForOtherContext', 'eLEXTYPE_RESERVED8',
    'eLEXTYPE_RESERVED4', 'DISPID_SGRsFindRule',
    'DISPID_SOTCreateInstance', 'SPINTERFERENCE_LATENCY_TRUNCATE_END',
    'ISpeechObjectTokenCategory', 'ISpeechAudioStatus',
    'SPSEMANTICERRORINFO', 'SpeechAllElements', 'ISpObjectWithToken',
    'DISPID_SAVolume', 'DISPID_SPIReplacements',
    'SREPropertyNumChange', 'SPSERIALIZEDRESULT',
    'Speech_StreamPos_RealTime', 'ISpNotifySource',
    'DISPID_SCSBaseStream', 'SPSHT_NotOverriden',
    'DISPID_SDKCreateKey', 'ISpPhraseAlt',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet',
    'SpeechGrammarState', 'SDTReplacement', 'SPEI_HYPOTHESIS',
    'SPRECOCONTEXTSTATUS', 'SVP_16', 'SECLowConfidence',
    'DISPID_SGRSTPropertyValue',
    'DISPID_SpeechGrammarRuleStateTransition',
    'DISPID_SRGSetWordSequenceData', 'SPSERIALIZEDPHRASE',
    'SpeechTokenKeyAttributes', 'SPEI_SR_BOOKMARK',
    'ISpeechGrammarRules', 'SASClosed', 'DISPID_SPIAudioSizeTime',
    'DISPID_SLGetGenerationChange', 'SpeechCategoryPhoneConverters',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_MS', 'UINT_PTR',
    'DISPID_SOTCGetDataKey', 'SRTReSent', 'DISPID_SRCEEndStream',
    'SECHighConfidence', 'SGRSTTEpsilon', 'DISPID_SPISaveToMemory',
    'DISPID_SDKEnumValues', 'STCInprocHandler',
    'DISPID_SLRemovePronunciation', 'DISPID_SRRRecoContext',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'ISpeechPhraseRule',
    'DISPID_SLPPhoneIds', 'DISPID_SpeechPhraseElement',
    'eLEXTYPE_MORPHOLOGY', 'IInternetSecurityManager', 'SAFTText',
    'SpeechPropertyComplexResponseSpeed', 'DISPID_SVEAudioLevel',
    'eLEXTYPE_PRIVATE2', 'SpSharedRecoContext', 'SPPS_LMA',
    'SREAdaptation', 'DISPIDSPTSI_SelectionOffset',
    'ISpeechFileStream', 'SPAR_Medium', 'SPSHORTCUTTYPE',
    'eLEXTYPE_APP', 'SpeechTokenValueCLSID',
    'SWPUnknownWordPronounceable', 'SVSFParseSapi',
    'SPINTERFERENCE_NOISE', 'SVSFIsNotXML', 'DISPID_SpeechFileStream',
    'SAFTADPCM_44kHzStereo', 'SPPS_Unknown', 'SPEI_INTERFERENCE',
    'SPRST_ACTIVE_ALWAYS', 'DISPID_SRRSetTextFeedback',
    'SpeechVisemeType', 'SPFM_OPEN_READWRITE', 'DISPID_SGRSTsCount',
    'SWTDeleted', 'SPEI_VISEME', 'SGRSTTWildcard', 'SVF_Stressed',
    'SVSFDefault', 'DISPID_SMSALineId', 'DISPID_SPAsCount',
    'DISPID_SABIMinNotification', 'ISpeechMemoryStream',
    'DISPID_SVSLastStreamNumberQueued', 'DISPID_SPIRetainedSizeBytes',
    'SAFTCCITT_ALaw_8kHzMono', 'ISpObjectToken', 'SGDSActive',
    'SpeechRecoContextState', 'SPCONTEXTSTATE', 'SVP_11',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet',
    'DISPID_SVSpeakStream', 'SAFT22kHz16BitMono', 'SPPS_Function',
    'LONG_PTR', 'DISPID_SpeechLexiconWord', 'SVEBookmark', 'SRARoot',
    'DISPID_SpeechGrammarRuleStateTransitions', 'DISPID_SASetState',
    'SECNormalConfidence', 'DISPID_SPIRule', 'SP_VISEME_12',
    'eLEXTYPE_PRIVATE20', 'SP_VISEME_5', 'SAFT24kHz16BitStereo',
    'DISPID_SpeechObjectTokenCategory', 'SWPKnownWordPronounceable',
    'DISPID_SPEActualConfidence', 'DISPID_SpeechPhraseInfo',
    'SPPARTOFSPEECH', 'SDTAlternates', 'DISPID_SPERetainedSizeBytes',
    'DISPID_SBSFormat', 'SPEI_RESERVED5', 'DISPID_SPARecoResult',
    'DISPID_SVSCurrentStreamNumber', 'DISPID_SAStatus', 'ISpVoice',
    'DISPID_SPRs_NewEnum', 'ISpPhoneticAlphabetSelection',
    'SDKLDefaultLocation', 'SRTEmulated', 'ISpeechPhraseElements',
    'DISPID_SPILanguageId', 'SAFTADPCM_8kHzMono',
    'DISPID_SVGetAudioOutputs', 'SAFT16kHz8BitMono',
    'SPINTERFERENCE_TOOFAST', 'SPSHT_Unknown', 'SVPAlert',
    'SAFT12kHz8BitStereo', 'DISPID_SpeechAudioBufferInfo',
    'SPEI_UNDEFINED', 'DISPID_SBSRead', 'SpeechPropertyResourceUsage',
    'SPEI_START_INPUT_STREAM', 'eLEXTYPE_VENDORLEXICON',
    'SPLEXICONTYPE', 'DISPID_SGRSAddRuleTransition',
    'DISPID_SAFGetWaveFormatEx', 'SDTAll', 'STSF_AppData',
    'SpeechVoiceSkipTypeSentence', 'SPRS_INACTIVE', 'SRAExport',
    'DISPID_SpeechRecoResult2', 'DISPID_SVSLastBookmark', 'SpStream',
    'SPEI_VOICE_CHANGE', 'DISPID_SPPParent', 'SVF_None',
    'SPVPRIORITY', 'DISPID_SVSInputSentencePosition',
    'DISPID_SPRuleEngineConfidence', 'DISPID_SRRAudioFormat',
    'DISPID_SpeechPhraseAlternate', 'DISPID_SpeechRecoResultTimes',
    'DISPID_SPCPhoneToId', 'ISpeechPhraseRules',
    'DISPID_SRSNumberOfActiveRules', 'DISPID_SRState',
    'SAFT44kHz8BitMono', 'DISPID_SPAStartElementInResult',
    'DISPID_SPEPronunciation', 'ISpeechRecognizerStatus',
    'DISPID_SGRName', 'SPGS_DISABLED', 'DISPID_SDKSetLongValue',
    'SREPhraseStart', 'SAFT16kHz16BitMono', 'DISPID_SGRClear',
    'DISPID_SVWaitUntilDone', 'DISPID_SVAudioOutput', 'SECFDefault',
    'DISPID_SRProfile', 'SAFTCCITT_ALaw_22kHzMono',
    'DISPID_SVEBookmark', 'DISPID_SGRsDynamic',
    'ISpPhoneticAlphabetConverter', 'DISPID_SGRSRule',
    'SDKLCurrentConfig', 'SSTTTextBuffer', 'SPGRAMMARWORDTYPE',
    'SVSFIsXML', 'SPDKL_DefaultLocation', 'SPCT_COMMAND',
    'DISPID_SVSpeakCompleteEvent', 'SVPNormal', 'SPDKL_LocalMachine',
    'DISPID_SRCEPhraseStart', 'DISPID_SPRsCount', 'SP_VISEME_2',
    'SP_VISEME_18', 'SPPHRASEELEMENT', 'SPBO_NONE', 'DISPID_SRRAudio',
    'SpeechPropertyHighConfidenceThreshold',
    'DISPID_SRCEFalseRecognition', 'SRTSMLTimeout',
    'DISPID_SLGetPronunciations', 'DISPID_SRRTStreamTime', 'SRSEDone',
    'DISPID_SPIGetDisplayAttributes', 'SVSFIsFilename',
    'SAFT16kHz8BitStereo', 'eLEXTYPE_PRIVATE10', 'SPBOOKMARKOPTIONS',
    'ISpeechVoiceStatus', 'DISPID_SVSyncronousSpeakTimeout',
    'SECFEmulateResult', 'SPWT_DISPLAY', 'SpVoice',
    'SPAO_RETAIN_AUDIO', 'SPINTERFERENCE', 'SpFileStream', 'SVP_2',
    'DISPID_SVEStreamEnd', 'SpeechInterference', 'SPSLMA',
    'DISPID_SOTIsUISupported', 'DISPID_SVSVisemeId',
    'SpeechGrammarTagWildcard', 'DISPID_SOTCategory', 'SPFILEMODE',
    'SPEI_MAX_TTS', 'SP_VISEME_6', 'SPCS_ENABLED', 'SREAudioLevel',
    'DISPID_SGRSAddWordTransition', 'SPWORDPRONUNCIATION',
    'DISPID_SGRSTType', 'SPDKL_CurrentUser', 'SpeechLexiconType',
    'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE', 'SITooFast',
    'Speech_Max_Word_Length', 'SPPS_RESERVED4',
    'DISPID_SRCCreateGrammar', 'SGDisplay', 'SAFT24kHz16BitMono',
    'SVSFNLPSpeakPunc', 'SWPUnknownWordUnpronounceable'
]

